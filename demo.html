<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D台球游戏演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            overflow: hidden;
            color: white;
        }

        #game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #game-canvas {
            display: block;
            width: 100%;
            height: 100%;
        }

        #ui-overlay {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 20px;
            border-radius: 10px;
            color: white;
        }

        #instructions {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .control-info {
            margin: 5px 0;
            font-size: 14px;
        }

        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-top: 5px solid #ffd700;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .loading-text {
            margin-top: 20px;
            font-size: 1.2em;
            color: #ffd700;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div id="game-container">
        <canvas id="game-canvas"></canvas>
        
        <div id="ui-overlay">
            <h3>3D台球游戏</h3>
            <div>状态: <span id="game-status">初始化中...</span></div>
            <div>当前玩家: <span id="current-player">玩家1</span></div>
            <div>力度: <span id="power-display">0%</span></div>
        </div>

        <div id="instructions">
            <div class="control-info"><strong>操作说明:</strong></div>
            <div class="control-info">鼠标移动: 瞄准方向</div>
            <div class="control-info">鼠标左键按住: 蓄力击球</div>
            <div class="control-info">鼠标滚轮: 调整视角距离</div>
            <div class="control-info">右键拖拽: 旋转视角</div>
        </div>
    </div>

    <div id="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">游戏加载中...</div>
    </div>

    <!-- 使用CDN加载Three.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r158/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cannon.js/0.20.0/cannon.min.js"></script>

    <script>
        // 简化的台球游戏演示
        class BilliardDemo {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.canvas = null;
                
                this.world = null;
                this.balls = [];
                this.table = null;
                
                this.mouse = new THREE.Vector2();
                this.raycaster = new THREE.Raycaster();
                this.isAiming = false;
                this.power = 0;
                this.currentPlayer = 1;
                
                this.clock = new THREE.Clock();
                this.animationId = null;
                
                this.init();
            }

            async init() {
                try {
                    this.updateStatus('初始化Three.js...');
                    this.initThreeJS();
                    
                    this.updateStatus('初始化物理引擎...');
                    this.initPhysics();
                    
                    this.updateStatus('创建台球桌...');
                    this.createTable();
                    
                    this.updateStatus('创建台球...');
                    this.createBalls();
                    
                    this.updateStatus('设置事件监听...');
                    this.setupEventListeners();
                    
                    this.updateStatus('启动游戏循环...');
                    this.startGameLoop();
                    
                    this.hideLoading();
                    this.updateStatus('游戏就绪');
                    
                } catch (error) {
                    console.error('游戏初始化失败:', error);
                    this.updateStatus('初始化失败: ' + error.message);
                }
            }

            initThreeJS() {
                this.canvas = document.getElementById('game-canvas');
                
                // 创建场景
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x1a1a1a);
                
                // 创建相机
                const aspect = window.innerWidth / window.innerHeight;
                this.camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);
                this.camera.position.set(0, 8, 12);
                this.camera.lookAt(0, 0, 0);
                
                // 创建渲染器
                this.renderer = new THREE.WebGLRenderer({ 
                    canvas: this.canvas,
                    antialias: true
                });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                
                // 添加光照
                const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
                this.scene.add(ambientLight);
                
                const mainLight = new THREE.DirectionalLight(0xffffff, 1);
                mainLight.position.set(10, 20, 10);
                mainLight.castShadow = true;
                mainLight.shadow.mapSize.width = 2048;
                mainLight.shadow.mapSize.height = 2048;
                this.scene.add(mainLight);
                
                const spotLight = new THREE.SpotLight(0xffffff, 0.8);
                spotLight.position.set(0, 15, 0);
                spotLight.target.position.set(0, 0, 0);
                spotLight.angle = Math.PI / 3;
                spotLight.castShadow = true;
                this.scene.add(spotLight);
                this.scene.add(spotLight.target);
            }

            initPhysics() {
                this.world = new CANNON.World();
                this.world.gravity.set(0, -9.82, 0);
                this.world.broadphase = new CANNON.NaiveBroadphase();
                this.world.solver.iterations = 10;
                this.world.allowSleep = true;
            }

            createTable() {
                // 台面
                const tableGeometry = new THREE.BoxGeometry(9, 0.1, 4.5);
                const tableMaterial = new THREE.MeshLambertMaterial({ color: 0x0d5016 });
                this.table = new THREE.Mesh(tableGeometry, tableMaterial);
                this.table.position.set(0, -0.05, 0);
                this.table.receiveShadow = true;
                this.scene.add(this.table);
                
                // 台面物理体
                const tableShape = new CANNON.Box(new CANNON.Vec3(4.5, 0.05, 2.25));
                const tableBody = new CANNON.Body({ mass: 0 });
                tableBody.addShape(tableShape);
                tableBody.position.set(0, -0.05, 0);
                this.world.add(tableBody);
                
                // 边框
                this.createCushions();
            }

            createCushions() {
                const cushionMaterial = new THREE.MeshLambertMaterial({ color: 0x2d5a2d });
                const cushionHeight = 0.1;
                const cushionThickness = 0.1;
                
                // 长边边框
                const longCushionGeometry = new THREE.BoxGeometry(9, cushionHeight, cushionThickness);
                
                // 上边框
                const topCushion = new THREE.Mesh(longCushionGeometry, cushionMaterial);
                topCushion.position.set(0, cushionHeight / 2, 2.25 + cushionThickness / 2);
                topCushion.castShadow = true;
                this.scene.add(topCushion);
                
                // 下边框
                const bottomCushion = new THREE.Mesh(longCushionGeometry, cushionMaterial);
                bottomCushion.position.set(0, cushionHeight / 2, -2.25 - cushionThickness / 2);
                bottomCushion.castShadow = true;
                this.scene.add(bottomCushion);
                
                // 短边边框
                const shortCushionGeometry = new THREE.BoxGeometry(cushionThickness, cushionHeight, 4.5);
                
                // 左边框
                const leftCushion = new THREE.Mesh(shortCushionGeometry, cushionMaterial);
                leftCushion.position.set(-4.5 - cushionThickness / 2, cushionHeight / 2, 0);
                leftCushion.castShadow = true;
                this.scene.add(leftCushion);
                
                // 右边框
                const rightCushion = new THREE.Mesh(shortCushionGeometry, cushionMaterial);
                rightCushion.position.set(4.5 + cushionThickness / 2, cushionHeight / 2, 0);
                rightCushion.castShadow = true;
                this.scene.add(rightCushion);
                
                // 边框物理体
                this.createCushionPhysics();
            }

            createCushionPhysics() {
                const cushionMaterial = new CANNON.Material();
                cushionMaterial.friction = 0.2;
                cushionMaterial.restitution = 0.8;
                
                // 上边框
                const topShape = new CANNON.Box(new CANNON.Vec3(4.5, 0.05, 0.05));
                const topBody = new CANNON.Body({ mass: 0, material: cushionMaterial });
                topBody.addShape(topShape);
                topBody.position.set(0, 0.05, 2.3);
                this.world.add(topBody);
                
                // 下边框
                const bottomBody = new CANNON.Body({ mass: 0, material: cushionMaterial });
                bottomBody.addShape(topShape);
                bottomBody.position.set(0, 0.05, -2.3);
                this.world.add(bottomBody);
                
                // 左边框
                const sideShape = new CANNON.Box(new CANNON.Vec3(0.05, 0.05, 2.25));
                const leftBody = new CANNON.Body({ mass: 0, material: cushionMaterial });
                leftBody.addShape(sideShape);
                leftBody.position.set(-4.55, 0.05, 0);
                this.world.add(leftBody);
                
                // 右边框
                const rightBody = new CANNON.Body({ mass: 0, material: cushionMaterial });
                rightBody.addShape(sideShape);
                rightBody.position.set(4.55, 0.05, 0);
                this.world.add(rightBody);
            }

            createBalls() {
                const ballRadius = 0.057;
                const ballMass = 0.16;
                
                // 球的颜色
                const colors = [
                    0xffffff, // 白球
                    0xffff00, 0x0000ff, 0xff0000, 0x800080, 0xffa500, 0x008000, 0x800000, // 1-7号球
                    0x000000, // 8号球
                    0xffff00, 0x0000ff, 0xff0000, 0x800080, 0xffa500, 0x008000, 0x800000  // 9-15号球
                ];
                
                // 创建白球
                this.createBall(0, -6, 0, 0, colors[0]);
                
                // 创建彩球（三角排列）
                const startX = 6;
                const spacing = ballRadius * 2.1;
                let ballIndex = 1;
                
                // 5排球的三角排列
                for (let row = 0; row < 5; row++) {
                    for (let col = 0; col <= row; col++) {
                        const x = startX + row * spacing;
                        const z = (col - row / 2) * spacing;
                        this.createBall(ballIndex, x, 0, z, colors[ballIndex]);
                        ballIndex++;
                    }
                }
            }

            createBall(number, x, y, z, color) {
                const ballRadius = 0.057;
                const ballMass = 0.16;
                
                // 创建球的几何体和材质
                const ballGeometry = new THREE.SphereGeometry(ballRadius, 32, 16);
                const ballMaterial = new THREE.MeshPhongMaterial({ 
                    color: color,
                    shininess: 100,
                    specular: 0x222222
                });
                
                const ballMesh = new THREE.Mesh(ballGeometry, ballMaterial);
                ballMesh.position.set(x, y + ballRadius, z);
                ballMesh.castShadow = true;
                ballMesh.receiveShadow = true;
                ballMesh.userData = { number: number, type: 'ball' };
                this.scene.add(ballMesh);
                
                // 创建物理体
                const ballShape = new CANNON.Sphere(ballRadius);
                const ballMaterial = new CANNON.Material();
                ballMaterial.friction = 0.1;
                ballMaterial.restitution = 0.9;
                
                const ballBody = new CANNON.Body({ 
                    mass: ballMass,
                    material: ballMaterial
                });
                ballBody.addShape(ballShape);
                ballBody.position.set(x, y + ballRadius, z);
                ballBody.linearDamping = 0.1;
                ballBody.angularDamping = 0.1;
                this.world.add(ballBody);
                
                this.balls.push({
                    number: number,
                    mesh: ballMesh,
                    body: ballBody
                });
            }

            setupEventListeners() {
                this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
                this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
                this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
                this.canvas.addEventListener('wheel', this.onMouseWheel.bind(this));
                window.addEventListener('resize', this.onWindowResize.bind(this));
            }

            onMouseMove(event) {
                const rect = this.canvas.getBoundingClientRect();
                this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
                this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
            }

            onMouseDown(event) {
                if (event.button === 0) { // 左键
                    this.startAiming();
                }
            }

            onMouseUp(event) {
                if (event.button === 0 && this.isAiming) { // 左键
                    this.shoot();
                }
            }

            onMouseWheel(event) {
                const delta = event.deltaY * 0.01;
                const direction = new THREE.Vector3();
                this.camera.getWorldDirection(direction);
                this.camera.position.addScaledVector(direction, delta);
            }

            onWindowResize() {
                this.camera.aspect = window.innerWidth / window.innerHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(window.innerWidth, window.innerHeight);
            }

            startAiming() {
                if (!this.canShoot()) return;
                
                this.isAiming = true;
                this.power = 0;
                this.startPowerAccumulation();
            }

            startPowerAccumulation() {
                const startTime = Date.now();
                const maxPower = 100;
                const accumTime = 2000; // 2秒达到最大力度
                
                const updatePower = () => {
                    if (!this.isAiming) return;
                    
                    const elapsed = Date.now() - startTime;
                    this.power = Math.min((elapsed / accumTime) * maxPower, maxPower);
                    
                    this.updatePowerDisplay(this.power);
                    
                    if (this.isAiming) {
                        requestAnimationFrame(updatePower);
                    }
                };
                
                updatePower();
            }

            shoot() {
                if (!this.isAiming || this.power === 0) return;
                
                this.isAiming = false;
                
                // 计算击球方向
                this.raycaster.setFromCamera(this.mouse, this.camera);
                const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0);
                const intersectPoint = new THREE.Vector3();
                this.raycaster.ray.intersectPlane(plane, intersectPoint);
                
                // 获取白球
                const cueBall = this.balls[0];
                const cueBallPos = cueBall.body.position;
                
                // 计算方向和力度
                const direction = new THREE.Vector3()
                    .subVectors(intersectPoint, new THREE.Vector3(cueBallPos.x, cueBallPos.y, cueBallPos.z))
                    .normalize();
                
                const force = (this.power / 100) * 15;
                
                // 应用力到白球
                const impulse = new CANNON.Vec3(
                    direction.x * force,
                    0,
                    direction.z * force
                );
                cueBall.body.applyImpulse(impulse);
                
                this.power = 0;
                this.updatePowerDisplay(0);
                
                // 等待球停止后切换玩家
                setTimeout(() => {
                    this.switchPlayer();
                }, 3000);
            }

            canShoot() {
                return this.balls.every(ball => 
                    ball.body.velocity.length() < 0.1 && 
                    ball.body.angularVelocity.length() < 0.1
                );
            }

            switchPlayer() {
                this.currentPlayer = this.currentPlayer === 1 ? 2 : 1;
                this.updateCurrentPlayer(this.currentPlayer);
            }

            startGameLoop() {
                const animate = () => {
                    this.animationId = requestAnimationFrame(animate);
                    
                    const deltaTime = this.clock.getDelta();
                    
                    // 更新物理
                    this.world.step(1/60, deltaTime, 3);
                    
                    // 同步球的位置
                    this.balls.forEach(ball => {
                        ball.mesh.position.copy(ball.body.position);
                        ball.mesh.quaternion.copy(ball.body.quaternion);
                    });
                    
                    // 渲染场景
                    this.renderer.render(this.scene, this.camera);
                };
                
                animate();
            }

            updateStatus(status) {
                const statusElement = document.getElementById('game-status');
                if (statusElement) {
                    statusElement.textContent = status;
                }
            }

            updateCurrentPlayer(player) {
                const playerElement = document.getElementById('current-player');
                if (playerElement) {
                    playerElement.textContent = `玩家${player}`;
                }
            }

            updatePowerDisplay(power) {
                const powerElement = document.getElementById('power-display');
                if (powerElement) {
                    powerElement.textContent = `${Math.round(power)}%`;
                }
            }

            hideLoading() {
                const loading = document.getElementById('loading');
                if (loading) {
                    loading.classList.add('hidden');
                }
            }
        }

        // 启动游戏
        window.addEventListener('load', () => {
            new BilliardDemo();
        });
    </script>
</body>
</html>
