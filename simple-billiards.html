<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单3D台球游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
        }
        
        #loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 20px;
            z-index: 1000;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div id="loading">游戏加载中...</div>
    <div id="info" class="hidden">
        <h3>3D台球游戏</h3>
        <p>鼠标移动: 瞄准</p>
        <p>鼠标左键: 击球</p>
        <p>滚轮: 调整视角</p>
    </div>

    <!-- 加载Three.js -->
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <!-- 加载Cannon.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cannon.js/0.6.2/cannon.min.js"></script>
    
    <script>
        class SimpleBilliards {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.world = null;
                this.balls = [];
                this.table = null;
                this.mouse = new THREE.Vector2();
                this.raycaster = new THREE.Raycaster();
                this.clock = new THREE.Clock();
            }

            async init() {
                try {
                    // 检查库是否加载
                    if (typeof THREE === 'undefined') {
                        throw new Error('Three.js 未加载');
                    }
                    if (typeof CANNON === 'undefined') {
                        throw new Error('Cannon.js 未加载');
                    }

                    this.initThreeJS();
                    this.initPhysics();
                    this.createTable();
                    this.createBalls();
                    this.setupEvents();
                    this.animate();
                    
                    document.getElementById('loading').classList.add('hidden');
                    document.getElementById('info').classList.remove('hidden');
                    
                    console.log('游戏初始化成功');
                } catch (error) {
                    console.error('游戏初始化失败:', error);
                    document.getElementById('loading').textContent = '游戏初始化失败: ' + error.message;
                }
            }

            initThreeJS() {
                // 创建场景
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x1a1a1a);

                // 创建相机
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                this.camera.position.set(0, 8, 12);
                this.camera.lookAt(0, 0, 0);

                // 创建渲染器
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                document.body.appendChild(this.renderer.domElement);

                // 添加光照
                const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
                this.scene.add(ambientLight);

                const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
                directionalLight.position.set(10, 20, 10);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                this.scene.add(directionalLight);

                const spotLight = new THREE.SpotLight(0xffffff, 0.8);
                spotLight.position.set(0, 15, 0);
                spotLight.target.position.set(0, 0, 0);
                spotLight.angle = Math.PI / 3;
                spotLight.castShadow = true;
                this.scene.add(spotLight);
                this.scene.add(spotLight.target);
            }

            initPhysics() {
                this.world = new CANNON.World();
                this.world.gravity.set(0, -9.82, 0);
                this.world.broadphase = new CANNON.NaiveBroadphase();
                this.world.solver.iterations = 10;
                this.world.allowSleep = true;
            }

            createTable() {
                // 台面
                const tableGeometry = new THREE.BoxGeometry(9, 0.1, 4.5);
                const tableMaterial = new THREE.MeshLambertMaterial({ color: 0x0d5016 });
                this.table = new THREE.Mesh(tableGeometry, tableMaterial);
                this.table.position.set(0, -0.05, 0);
                this.table.receiveShadow = true;
                this.scene.add(this.table);

                // 台面物理体
                const tableShape = new CANNON.Box(new CANNON.Vec3(4.5, 0.05, 2.25));
                const tableBody = new CANNON.Body({ mass: 0 });
                tableBody.addShape(tableShape);
                tableBody.position.set(0, -0.05, 0);
                this.world.add(tableBody);

                // 边框
                this.createCushions();
            }

            createCushions() {
                const cushionMaterial = new THREE.MeshLambertMaterial({ color: 0x2d5a2d });
                const cushionHeight = 0.1;
                const cushionThickness = 0.1;

                const positions = [
                    { pos: [0, cushionHeight / 2, 2.25 + cushionThickness / 2], size: [4.5, cushionHeight / 2, cushionThickness / 2] },
                    { pos: [0, cushionHeight / 2, -2.25 - cushionThickness / 2], size: [4.5, cushionHeight / 2, cushionThickness / 2] },
                    { pos: [-4.5 - cushionThickness / 2, cushionHeight / 2, 0], size: [cushionThickness / 2, cushionHeight / 2, 2.25] },
                    { pos: [4.5 + cushionThickness / 2, cushionHeight / 2, 0], size: [cushionThickness / 2, cushionHeight / 2, 2.25] }
                ];

                positions.forEach(({ pos, size }) => {
                    // 视觉边框
                    const geometry = new THREE.BoxGeometry(size[0] * 2, size[1] * 2, size[2] * 2);
                    const mesh = new THREE.Mesh(geometry, cushionMaterial);
                    mesh.position.set(...pos);
                    mesh.castShadow = true;
                    this.scene.add(mesh);

                    // 物理边框
                    const shape = new CANNON.Box(new CANNON.Vec3(...size));
                    const body = new CANNON.Body({ mass: 0 });
                    body.addShape(shape);
                    body.position.set(...pos);
                    this.world.add(body);
                });
            }

            createBalls() {
                const ballRadius = 0.057;
                const ballMass = 0.16;

                // 球的颜色
                const colors = [
                    0xffffff, // 白球
                    0xffff00, 0x0000ff, 0xff0000, 0x800080, 0xffa500, 0x008000, 0x800000, // 1-7号球
                    0x000000, // 8号球
                    0xffff00, 0x0000ff, 0xff0000, 0x800080, 0xffa500, 0x008000, 0x800000  // 9-15号球
                ];

                // 创建白球
                this.createBall(0, -6, 0, 0, colors[0]);

                // 创建彩球（三角排列）
                const startX = 6;
                const spacing = ballRadius * 2.1;
                let ballIndex = 1;

                for (let row = 0; row < 5; row++) {
                    for (let col = 0; col <= row; col++) {
                        const x = startX + row * spacing;
                        const z = (col - row / 2) * spacing;
                        this.createBall(ballIndex, x, 0, z, colors[ballIndex]);
                        ballIndex++;
                    }
                }
            }

            createBall(number, x, y, z, color) {
                const ballRadius = 0.057;
                const ballMass = 0.16;

                // 创建球的几何体和材质
                const ballGeometry = new THREE.SphereGeometry(ballRadius, 32, 16);
                const ballMaterial = new THREE.MeshPhongMaterial({ 
                    color: color,
                    shininess: 100,
                    specular: 0x222222
                });

                const ballMesh = new THREE.Mesh(ballGeometry, ballMaterial);
                ballMesh.position.set(x, y + ballRadius, z);
                ballMesh.castShadow = true;
                ballMesh.receiveShadow = true;
                ballMesh.userData = { number: number, type: 'ball' };
                this.scene.add(ballMesh);

                // 创建物理体
                const ballShape = new CANNON.Sphere(ballRadius);
                const ballBody = new CANNON.Body({ 
                    mass: ballMass
                });
                ballBody.addShape(ballShape);
                ballBody.position.set(x, y + ballRadius, z);
                ballBody.linearDamping = 0.1;
                ballBody.angularDamping = 0.1;
                this.world.add(ballBody);

                this.balls.push({
                    number: number,
                    mesh: ballMesh,
                    body: ballBody
                });
            }

            setupEvents() {
                // 鼠标事件
                document.addEventListener('mousemove', (event) => {
                    this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
                    this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
                });

                document.addEventListener('mousedown', (event) => {
                    if (event.button === 0) {
                        this.shoot();
                    }
                });

                // 滚轮事件
                document.addEventListener('wheel', (event) => {
                    const delta = event.deltaY * 0.01;
                    const direction = new THREE.Vector3();
                    this.camera.getWorldDirection(direction);
                    this.camera.position.addScaledVector(direction, delta);
                });

                // 窗口大小调整
                window.addEventListener('resize', () => {
                    this.camera.aspect = window.innerWidth / window.innerHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                });
            }

            shoot() {
                // 计算击球方向
                this.raycaster.setFromCamera(this.mouse, this.camera);
                const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0);
                const intersectPoint = new THREE.Vector3();
                this.raycaster.ray.intersectPlane(plane, intersectPoint);

                // 获取白球
                const cueBall = this.balls[0];
                const cueBallPos = cueBall.body.position;

                // 计算方向和力度
                const direction = new THREE.Vector3()
                    .subVectors(intersectPoint, new THREE.Vector3(cueBallPos.x, cueBallPos.y, cueBallPos.z))
                    .normalize();

                const force = 10; // 固定力度

                // 应用力到白球
                const impulse = new CANNON.Vec3(
                    direction.x * force,
                    0,
                    direction.z * force
                );
                cueBall.body.applyImpulse(impulse);
            }

            animate() {
                requestAnimationFrame(() => this.animate());

                const deltaTime = this.clock.getDelta();

                // 更新物理
                this.world.step(1/60, deltaTime, 3);

                // 同步球的位置
                this.balls.forEach(ball => {
                    ball.mesh.position.copy(ball.body.position);
                    ball.mesh.quaternion.copy(ball.body.quaternion);
                });

                // 渲染场景
                this.renderer.render(this.scene, this.camera);
            }
        }

        // 等待页面加载完成后初始化游戏
        window.addEventListener('load', () => {
            const game = new SimpleBilliards();
            game.init();
        });
    </script>
</body>
</html>
