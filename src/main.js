// 使用动态导入来加载模块
async function loadModules() {
    const { Game } = await import('./game/Game.js');
    const { UI } = await import('./game/UI.js');
    return { Game, UI };
}

class App {
    constructor() {
        this.game = null;
        this.ui = null;
        this.isInitialized = false;
        this.Game = null;
        this.UI = null;

        this.init();
    }

    async init() {
        try {
            // 显示加载界面
            this.showLoading();

            // 加载模块
            const modules = await loadModules();
            this.Game = modules.Game;
            this.UI = modules.UI;

            // 初始化UI
            this.ui = new this.UI();
            await this.ui.init();
            
            // 设置UI事件监听
            this.setupUIEvents();
            
            // 隐藏加载界面，显示主菜单
            this.hideLoading();
            this.ui.showMainMenu();
            
            this.isInitialized = true;
            console.log('台球游戏初始化完成');
            
        } catch (error) {
            console.error('游戏初始化失败:', error);
            this.showError('游戏初始化失败，请刷新页面重试');
        }
    }

    setupUIEvents() {
        // 单人游戏
        this.ui.on('singlePlayer', () => {
            this.startGame('single');
        });

        // 双人游戏
        this.ui.on('twoPlayer', () => {
            this.startGame('two');
        });

        // 游戏设置
        this.ui.on('settings', () => {
            this.ui.showSettings();
        });

        // 游戏帮助
        this.ui.on('help', () => {
            this.ui.showHelp();
        });

        // 暂停游戏
        this.ui.on('pause', () => {
            if (this.game) {
                this.game.pause();
            }
        });

        // 重新开始
        this.ui.on('reset', () => {
            if (this.game) {
                this.game.reset();
            }
        });

        // 返回菜单
        this.ui.on('backToMenu', () => {
            if (this.game) {
                this.game.destroy();
                this.game = null;
            }
            this.ui.showMainMenu();
        });

        // 再玩一局
        this.ui.on('playAgain', () => {
            if (this.game) {
                this.game.reset();
                this.ui.showGameHUD();
            }
        });

        // 设置变更
        this.ui.on('settingsChanged', (settings) => {
            if (this.game) {
                this.game.updateSettings(settings);
            }
        });
    }

    async startGame(mode) {
        try {
            this.showLoading();
            
            // 销毁之前的游戏实例
            if (this.game) {
                this.game.destroy();
            }

            // 创建新游戏
            this.game = new this.Game(mode);
            await this.game.init();

            // 设置游戏事件监听
            this.setupGameEvents();

            // 显示游戏界面
            this.ui.showGameHUD();
            this.hideLoading();

            console.log(`开始${mode === 'single' ? '单人' : '双人'}游戏`);

        } catch (error) {
            console.error('游戏启动失败:', error);
            this.showError('游戏启动失败，请重试');
            this.hideLoading();
        }
    }

    setupGameEvents() {
        if (!this.game) return;

        // 游戏状态更新
        this.game.on('scoreUpdate', (scores) => {
            this.ui.updateScores(scores);
        });

        this.game.on('playerChange', (player) => {
            this.ui.updateCurrentPlayer(player);
        });

        this.game.on('powerChange', (power) => {
            this.ui.updatePower(power);
        });

        this.game.on('gameOver', (winner) => {
            this.ui.showGameOver(winner);
        });

        this.game.on('foul', (message) => {
            this.ui.showMessage(message);
        });

        // 错误处理
        this.game.on('error', (error) => {
            console.error('游戏错误:', error);
            this.showError('游戏出现错误: ' + error.message);
        });
    }

    showLoading() {
        const loading = document.getElementById('loading');
        if (loading) {
            loading.classList.remove('hidden');
        }
    }

    hideLoading() {
        const loading = document.getElementById('loading');
        if (loading) {
            loading.classList.add('hidden');
        }
    }

    showError(message) {
        alert(message); // 简单的错误显示，可以后续改进
    }

    // 处理窗口大小变化
    handleResize() {
        if (this.game) {
            this.game.handleResize();
        }
    }

    // 清理资源
    destroy() {
        if (this.game) {
            this.game.destroy();
        }
        if (this.ui) {
            this.ui.destroy();
        }
    }
}

// 全局事件监听
window.addEventListener('resize', () => {
    if (window.app) {
        window.app.handleResize();
    }
});

window.addEventListener('beforeunload', () => {
    if (window.app) {
        window.app.destroy();
    }
});

// 启动应用
window.app = new App();
