// 使用全局的THREE变量

export class Table {
    constructor() {
        this.mesh = null;
        this.cushions = [];
        this.pockets = [];
        this.physics = null;
        this.scene = null;
        
        // 台球桌尺寸 (标准9英尺台球桌)
        this.width = 4.5;  // 宽度
        this.length = 9;   // 长度
        this.height = 0.1; // 厚度
        
        // 边框参数
        this.cushionHeight = 0.1;
        this.cushionThickness = 0.1;
        
        // 球袋参数
        this.pocketRadius = 0.12;
        this.pocketDepth = 0.2;
        
        this.isInitialized = false;
    }

    async init(scene, physics) {
        try {
            this.scene = scene;
            this.physics = physics;
            
            // 创建台球桌几何体
            await this.createTable();
            
            // 创建边框
            await this.createCushions();
            
            // 创建球袋
            await this.createPockets();
            
            // 创建物理体
            this.createPhysicsBodies();
            
            this.isInitialized = true;
            console.log('台球桌创建完成');
            
        } catch (error) {
            console.error('台球桌创建失败:', error);
            throw error;
        }
    }

    async createTable() {
        // 创建台面几何体
        const tableGeometry = new THREE.BoxGeometry(this.length, this.height, this.width);
        
        // 创建台面材质
        const tableMaterial = new THREE.MeshLambertMaterial({
            color: 0x0d5016, // 深绿色
            roughness: 0.8,
            metalness: 0.1
        });
        
        // 创建台面网格
        this.mesh = new THREE.Mesh(tableGeometry, tableMaterial);
        this.mesh.position.set(0, -this.height / 2, 0);
        this.mesh.receiveShadow = true;
        this.mesh.castShadow = false;
        
        // 添加到场景
        this.scene.add(this.mesh);
        
        // 创建台球桌边框装饰
        await this.createTableFrame();
        
        // 添加台面纹理和标记
        await this.createTableMarkings();
    }

    async createTableFrame() {
        // 创建台球桌的木质边框
        const frameHeight = 0.3;
        const frameThickness = 0.2;
        
        const frameGeometry = new THREE.BoxGeometry(
            this.length + frameThickness * 2,
            frameHeight,
            this.width + frameThickness * 2
        );
        
        const frameMaterial = new THREE.MeshLambertMaterial({
            color: 0x8B4513, // 棕色木质
            roughness: 0.9,
            metalness: 0.1
        });
        
        const frame = new THREE.Mesh(frameGeometry, frameMaterial);
        frame.position.set(0, -frameHeight / 2 - this.height, 0);
        frame.receiveShadow = true;
        frame.castShadow = true;
        
        this.scene.add(frame);
    }

    async createTableMarkings() {
        // 创建台球桌上的标记线和点
        
        // 中线
        const centerLineGeometry = new THREE.PlaneGeometry(0.02, this.width);
        const centerLineMaterial = new THREE.MeshBasicMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.8
        });
        
        const centerLine = new THREE.Mesh(centerLineGeometry, centerLineMaterial);
        centerLine.rotation.x = -Math.PI / 2;
        centerLine.position.set(0, 0.001, 0);
        this.scene.add(centerLine);
        
        // 开球线
        const breakLineGeometry = new THREE.PlaneGeometry(0.02, this.width);
        const breakLine = new THREE.Mesh(breakLineGeometry, centerLineMaterial);
        breakLine.rotation.x = -Math.PI / 2;
        breakLine.position.set(-this.length / 4, 0.001, 0);
        this.scene.add(breakLine);
        
        // 中心点
        const centerDotGeometry = new THREE.CircleGeometry(0.02, 16);
        const centerDot = new THREE.Mesh(centerDotGeometry, centerLineMaterial);
        centerDot.rotation.x = -Math.PI / 2;
        centerDot.position.set(0, 0.002, 0);
        this.scene.add(centerDot);
        
        // 开球点
        const breakDot = new THREE.Mesh(centerDotGeometry, centerLineMaterial);
        breakDot.rotation.x = -Math.PI / 2;
        breakDot.position.set(-this.length / 4, 0.002, 0);
        this.scene.add(breakDot);
    }

    async createCushions() {
        // 创建边框几何体和材质
        const cushionMaterial = new THREE.MeshLambertMaterial({
            color: 0x2d5a2d, // 深绿色，比台面稍深
            roughness: 0.7,
            metalness: 0.2
        });
        
        // 长边边框
        const longCushionGeometry = new THREE.BoxGeometry(
            this.length,
            this.cushionHeight,
            this.cushionThickness
        );
        
        // 上边框
        const topCushion = new THREE.Mesh(longCushionGeometry, cushionMaterial);
        topCushion.position.set(0, this.cushionHeight / 2, this.width / 2 + this.cushionThickness / 2);
        topCushion.castShadow = true;
        topCushion.receiveShadow = true;
        this.scene.add(topCushion);
        this.cushions.push(topCushion);
        
        // 下边框
        const bottomCushion = new THREE.Mesh(longCushionGeometry, cushionMaterial);
        bottomCushion.position.set(0, this.cushionHeight / 2, -this.width / 2 - this.cushionThickness / 2);
        bottomCushion.castShadow = true;
        bottomCushion.receiveShadow = true;
        this.scene.add(bottomCushion);
        this.cushions.push(bottomCushion);
        
        // 短边边框
        const shortCushionGeometry = new THREE.BoxGeometry(
            this.cushionThickness,
            this.cushionHeight,
            this.width
        );
        
        // 左边框
        const leftCushion = new THREE.Mesh(shortCushionGeometry, cushionMaterial);
        leftCushion.position.set(-this.length / 2 - this.cushionThickness / 2, this.cushionHeight / 2, 0);
        leftCushion.castShadow = true;
        leftCushion.receiveShadow = true;
        this.scene.add(leftCushion);
        this.cushions.push(leftCushion);
        
        // 右边框
        const rightCushion = new THREE.Mesh(shortCushionGeometry, cushionMaterial);
        rightCushion.position.set(this.length / 2 + this.cushionThickness / 2, this.cushionHeight / 2, 0);
        rightCushion.castShadow = true;
        rightCushion.receiveShadow = true;
        this.scene.add(rightCushion);
        this.cushions.push(rightCushion);
    }

    async createPockets() {
        // 球袋位置
        const pocketPositions = [
            // 四个角袋
            { x: -this.length / 2, z: -this.width / 2, name: 'corner1' },
            { x: -this.length / 2, z: this.width / 2, name: 'corner2' },
            { x: this.length / 2, z: -this.width / 2, name: 'corner3' },
            { x: this.length / 2, z: this.width / 2, name: 'corner4' },
            // 两个中袋
            { x: 0, z: -this.width / 2, name: 'side1' },
            { x: 0, z: this.width / 2, name: 'side2' }
        ];
        
        // 球袋材质
        const pocketMaterial = new THREE.MeshLambertMaterial({
            color: 0x000000, // 黑色
            roughness: 0.9,
            metalness: 0.1
        });
        
        pocketPositions.forEach(pos => {
            // 创建球袋几何体
            const pocketGeometry = new THREE.CylinderGeometry(
                this.pocketRadius,
                this.pocketRadius * 0.8,
                this.pocketDepth,
                16
            );
            
            const pocket = new THREE.Mesh(pocketGeometry, pocketMaterial);
            pocket.position.set(pos.x, -this.pocketDepth / 2, pos.z);
            pocket.receiveShadow = true;
            pocket.userData = { name: pos.name, isPocket: true };
            
            this.scene.add(pocket);
            this.pockets.push(pocket);
            
            // 添加球袋边缘装饰
            this.createPocketRim(pos.x, pos.z);
        });
    }

    createPocketRim(x, z) {
        // 创建球袋边缘的金属装饰
        const rimGeometry = new THREE.TorusGeometry(
            this.pocketRadius + 0.01,
            0.005,
            8,
            16
        );
        
        const rimMaterial = new THREE.MeshLambertMaterial({
            color: 0xC0C0C0, // 银色
            metalness: 0.8,
            roughness: 0.2
        });
        
        const rim = new THREE.Mesh(rimGeometry, rimMaterial);
        rim.position.set(x, 0.002, z);
        rim.rotation.x = Math.PI / 2;
        rim.castShadow = true;
        
        this.scene.add(rim);
    }

    createPhysicsBodies() {
        if (!this.physics) return;
        
        // 创建台面物理体
        this.physics.createTableBody(this.width, this.height, this.length);
        
        // 创建边框物理体
        this.physics.createCushionBodies(this.width, this.length, this.cushionHeight, this.cushionThickness);
        
        // 创建球袋物理体
        this.physics.createPocketBodies(this.width, this.length, this.pocketRadius);
    }

    // 检查球是否进袋
    checkBallInPocket(ballPosition, ballRadius = 0.057) {
        for (let i = 0; i < this.pockets.length; i++) {
            const pocket = this.pockets[i];
            const pocketPos = pocket.position;
            
            const distance = Math.sqrt(
                Math.pow(ballPosition.x - pocketPos.x, 2) +
                Math.pow(ballPosition.z - pocketPos.z, 2)
            );
            
            // 如果球心距离球袋中心小于球袋半径减去球半径
            if (distance < this.pocketRadius - ballRadius && ballPosition.y < 0) {
                return {
                    inPocket: true,
                    pocketIndex: i,
                    pocketName: pocket.userData.name
                };
            }
        }
        
        return { inPocket: false };
    }

    // 获取台球桌边界
    getBounds() {
        return {
            minX: -this.length / 2,
            maxX: this.length / 2,
            minZ: -this.width / 2,
            maxZ: this.width / 2,
            minY: 0,
            maxY: this.cushionHeight
        };
    }

    // 检查位置是否在台球桌上
    isPositionOnTable(position) {
        const bounds = this.getBounds();
        return position.x >= bounds.minX && position.x <= bounds.maxX &&
               position.z >= bounds.minZ && position.z <= bounds.maxZ &&
               position.y >= bounds.minY;
    }

    // 获取有效的球位置（避免重叠）
    getValidBallPosition(excludePositions = [], minDistance = 0.12) {
        const bounds = this.getBounds();
        const maxAttempts = 100;
        
        for (let attempt = 0; attempt < maxAttempts; attempt++) {
            const position = {
                x: THREE.MathUtils.lerp(bounds.minX + 0.5, bounds.maxX - 0.5, Math.random()),
                y: 0.057, // 球半径
                z: THREE.MathUtils.lerp(bounds.minZ + 0.5, bounds.maxZ - 0.5, Math.random())
            };
            
            // 检查是否与现有位置冲突
            let valid = true;
            for (const existingPos of excludePositions) {
                const distance = Math.sqrt(
                    Math.pow(position.x - existingPos.x, 2) +
                    Math.pow(position.z - existingPos.z, 2)
                );
                
                if (distance < minDistance) {
                    valid = false;
                    break;
                }
            }
            
            // 检查是否在球袋附近
            for (const pocket of this.pockets) {
                const distance = Math.sqrt(
                    Math.pow(position.x - pocket.position.x, 2) +
                    Math.pow(position.z - pocket.position.z, 2)
                );
                
                if (distance < this.pocketRadius + 0.1) {
                    valid = false;
                    break;
                }
            }
            
            if (valid) {
                return position;
            }
        }
        
        // 如果找不到有效位置，返回默认位置
        return { x: 0, y: 0.057, z: 0 };
    }

    // 更新台球桌状态
    update(deltaTime) {
        // 这里可以添加动画效果，比如球袋的光效等
    }

    // 获取台球桌信息
    getInfo() {
        return {
            width: this.width,
            length: this.length,
            height: this.height,
            pocketCount: this.pockets.length,
            cushionCount: this.cushions.length,
            bounds: this.getBounds()
        };
    }

    // 清理资源
    destroy() {
        // 从场景中移除台球桌
        if (this.mesh && this.scene) {
            this.scene.remove(this.mesh);
            this.mesh.geometry.dispose();
            this.mesh.material.dispose();
        }
        
        // 移除边框
        this.cushions.forEach(cushion => {
            if (this.scene) {
                this.scene.remove(cushion);
                cushion.geometry.dispose();
                cushion.material.dispose();
            }
        });
        
        // 移除球袋
        this.pockets.forEach(pocket => {
            if (this.scene) {
                this.scene.remove(pocket);
                pocket.geometry.dispose();
                pocket.material.dispose();
            }
        });
        
        this.mesh = null;
        this.cushions = [];
        this.pockets = [];
        this.physics = null;
        this.scene = null;
        this.isInitialized = false;
    }
}
