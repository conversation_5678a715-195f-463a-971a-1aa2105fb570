import { EventEmitter } from '../utils/EventEmitter.js';

export class UI extends EventEmitter {
    constructor() {
        super();
        
        this.elements = {};
        this.currentPanel = null;
        this.settings = {
            soundVolume: 50,
            graphicsQuality: 'medium',
            showTrajectory: true
        };
        
        this.isInitialized = false;
    }

    async init() {
        try {
            // 获取所有UI元素
            this.getElements();
            
            // 设置事件监听
            this.setupEventListeners();
            
            // 初始化设置
            this.initializeSettings();
            
            this.isInitialized = true;
            console.log('UI初始化完成');
            
        } catch (error) {
            console.error('UI初始化失败:', error);
            throw error;
        }
    }

    getElements() {
        // 主要面板
        this.elements.mainMenu = document.getElementById('main-menu');
        this.elements.gameHUD = document.getElementById('game-hud');
        this.elements.settingsPanel = document.getElementById('settings-panel');
        this.elements.helpPanel = document.getElementById('help-panel');
        this.elements.gameOverPanel = document.getElementById('game-over-panel');
        
        // 按钮
        this.elements.singlePlayerBtn = document.getElementById('single-player-btn');
        this.elements.twoPlayerBtn = document.getElementById('two-player-btn');
        this.elements.settingsBtn = document.getElementById('settings-btn');
        this.elements.helpBtn = document.getElementById('help-btn');
        this.elements.pauseBtn = document.getElementById('pause-btn');
        this.elements.resetBtn = document.getElementById('reset-btn');
        this.elements.menuBtn = document.getElementById('menu-btn');
        this.elements.settingsBackBtn = document.getElementById('settings-back-btn');
        this.elements.helpBackBtn = document.getElementById('help-back-btn');
        this.elements.playAgainBtn = document.getElementById('play-again-btn');
        this.elements.backToMenuBtn = document.getElementById('back-to-menu-btn');
        
        // 游戏信息显示
        this.elements.player1Score = document.getElementById('player1-score');
        this.elements.player2Score = document.getElementById('player2-score');
        this.elements.currentPlayerName = document.getElementById('current-player-name');
        this.elements.powerFill = document.getElementById('power-fill');
        this.elements.powerValue = document.getElementById('power-value');
        this.elements.winnerText = document.getElementById('winner-text');
        
        // 设置控件
        this.elements.soundVolume = document.getElementById('sound-volume');
        this.elements.graphicsQuality = document.getElementById('graphics-quality');
        this.elements.showTrajectory = document.getElementById('show-trajectory');
    }

    setupEventListeners() {
        // 主菜单按钮
        this.elements.singlePlayerBtn?.addEventListener('click', () => {
            this.emit('singlePlayer');
        });
        
        this.elements.twoPlayerBtn?.addEventListener('click', () => {
            this.emit('twoPlayer');
        });
        
        this.elements.settingsBtn?.addEventListener('click', () => {
            this.emit('settings');
        });
        
        this.elements.helpBtn?.addEventListener('click', () => {
            this.emit('help');
        });
        
        // 游戏控制按钮
        this.elements.pauseBtn?.addEventListener('click', () => {
            this.emit('pause');
        });
        
        this.elements.resetBtn?.addEventListener('click', () => {
            if (confirm('确定要重新开始游戏吗？')) {
                this.emit('reset');
            }
        });
        
        this.elements.menuBtn?.addEventListener('click', () => {
            if (confirm('确定要返回主菜单吗？当前游戏进度将丢失。')) {
                this.emit('backToMenu');
            }
        });
        
        // 返回按钮
        this.elements.settingsBackBtn?.addEventListener('click', () => {
            this.showMainMenu();
        });
        
        this.elements.helpBackBtn?.addEventListener('click', () => {
            this.showMainMenu();
        });
        
        // 游戏结束按钮
        this.elements.playAgainBtn?.addEventListener('click', () => {
            this.emit('playAgain');
        });
        
        this.elements.backToMenuBtn?.addEventListener('click', () => {
            this.emit('backToMenu');
        });
        
        // 设置变更监听
        this.elements.soundVolume?.addEventListener('input', (e) => {
            this.settings.soundVolume = parseInt(e.target.value);
            this.emit('settingsChanged', this.settings);
        });
        
        this.elements.graphicsQuality?.addEventListener('change', (e) => {
            this.settings.graphicsQuality = e.target.value;
            this.emit('settingsChanged', this.settings);
        });
        
        this.elements.showTrajectory?.addEventListener('change', (e) => {
            this.settings.showTrajectory = e.target.checked;
            this.emit('settingsChanged', this.settings);
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyPress(e);
        });
    }

    handleKeyPress(event) {
        switch (event.code) {
            case 'Escape':
                if (this.currentPanel === 'game') {
                    this.emit('pause');
                } else if (this.currentPanel !== 'main') {
                    this.showMainMenu();
                }
                break;
            case 'F1':
                event.preventDefault();
                this.showHelp();
                break;
        }
    }

    initializeSettings() {
        // 设置默认值
        if (this.elements.soundVolume) {
            this.elements.soundVolume.value = this.settings.soundVolume;
        }
        
        if (this.elements.graphicsQuality) {
            this.elements.graphicsQuality.value = this.settings.graphicsQuality;
        }
        
        if (this.elements.showTrajectory) {
            this.elements.showTrajectory.checked = this.settings.showTrajectory;
        }
    }

    // 显示主菜单
    showMainMenu() {
        this.hideAllPanels();
        this.elements.mainMenu?.classList.remove('hidden');
        this.currentPanel = 'main';
    }

    // 显示游戏界面
    showGameHUD() {
        this.hideAllPanels();
        this.elements.gameHUD?.classList.remove('hidden');
        this.currentPanel = 'game';
    }

    // 显示设置面板
    showSettings() {
        this.hideAllPanels();
        this.elements.settingsPanel?.classList.remove('hidden');
        this.currentPanel = 'settings';
    }

    // 显示帮助面板
    showHelp() {
        this.hideAllPanels();
        this.elements.helpPanel?.classList.remove('hidden');
        this.currentPanel = 'help';
    }

    // 显示游戏结束面板
    showGameOver(winner) {
        this.hideAllPanels();
        this.elements.gameOverPanel?.classList.remove('hidden');
        
        if (this.elements.winnerText) {
            this.elements.winnerText.textContent = `玩家${winner} 获胜！`;
        }
        
        this.currentPanel = 'gameOver';
    }

    // 隐藏所有面板
    hideAllPanels() {
        const panels = [
            this.elements.mainMenu,
            this.elements.gameHUD,
            this.elements.settingsPanel,
            this.elements.helpPanel,
            this.elements.gameOverPanel
        ];
        
        panels.forEach(panel => {
            panel?.classList.add('hidden');
        });
    }

    // 更新分数显示
    updateScores(scores) {
        if (this.elements.player1Score) {
            this.elements.player1Score.textContent = scores.player1;
        }
        
        if (this.elements.player2Score) {
            this.elements.player2Score.textContent = scores.player2;
        }
    }

    // 更新当前玩家显示
    updateCurrentPlayer(player) {
        if (this.elements.currentPlayerName) {
            this.elements.currentPlayerName.textContent = `玩家${player}`;
        }
        
        // 高亮当前玩家的分数板
        this.highlightCurrentPlayer(player);
    }

    highlightCurrentPlayer(player) {
        const scoreboard = document.getElementById('scoreboard');
        if (!scoreboard) return;
        
        const playerScores = scoreboard.querySelectorAll('.player-score');
        playerScores.forEach((scoreElement, index) => {
            if (index + 1 === player) {
                scoreElement.style.borderColor = '#4CAF50';
                scoreElement.style.backgroundColor = 'rgba(76, 175, 80, 0.1)';
            } else {
                scoreElement.style.borderColor = '#ffd700';
                scoreElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            }
        });
    }

    // 更新力度显示
    updatePower(power) {
        if (this.elements.powerFill) {
            this.elements.powerFill.style.width = `${power}%`;
        }
        
        if (this.elements.powerValue) {
            this.elements.powerValue.textContent = `${Math.round(power)}%`;
        }
    }

    // 显示消息
    showMessage(message, type = 'info', duration = 3000) {
        // 创建消息元素
        const messageElement = document.createElement('div');
        messageElement.className = `game-message ${type}`;
        messageElement.textContent = message;
        
        // 设置样式
        Object.assign(messageElement.style, {
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            background: type === 'error' ? 'rgba(244, 67, 54, 0.9)' : 'rgba(76, 175, 80, 0.9)',
            color: 'white',
            padding: '20px 30px',
            borderRadius: '10px',
            fontSize: '1.2em',
            fontWeight: 'bold',
            zIndex: '1000',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
            animation: 'fadeInOut 0.3s ease-in-out'
        });
        
        // 添加到页面
        document.body.appendChild(messageElement);
        
        // 自动移除
        setTimeout(() => {
            if (messageElement.parentNode) {
                messageElement.parentNode.removeChild(messageElement);
            }
        }, duration);
    }

    // 显示加载状态
    showLoading(message = '加载中...') {
        const loading = document.getElementById('loading');
        if (loading) {
            const loadingText = loading.querySelector('.loading-text');
            if (loadingText) {
                loadingText.textContent = message;
            }
            loading.classList.remove('hidden');
        }
    }

    // 隐藏加载状态
    hideLoading() {
        const loading = document.getElementById('loading');
        if (loading) {
            loading.classList.add('hidden');
        }
    }

    // 更新游戏统计
    updateGameStats(stats) {
        // 可以在这里添加游戏统计信息的显示
        // 比如游戏时间、击球次数等
    }

    // 显示暂停菜单
    showPauseMenu() {
        const pauseMenu = document.createElement('div');
        pauseMenu.id = 'pause-menu';
        pauseMenu.className = 'menu-panel';
        pauseMenu.innerHTML = `
            <h2>游戏暂停</h2>
            <div class="menu-buttons">
                <button id="resume-btn" class="menu-btn">继续游戏</button>
                <button id="restart-btn" class="menu-btn">重新开始</button>
                <button id="main-menu-btn" class="menu-btn">返回主菜单</button>
            </div>
        `;
        
        document.body.appendChild(pauseMenu);
        
        // 添加事件监听
        document.getElementById('resume-btn')?.addEventListener('click', () => {
            this.hidePauseMenu();
            this.emit('resume');
        });
        
        document.getElementById('restart-btn')?.addEventListener('click', () => {
            if (confirm('确定要重新开始吗？')) {
                this.hidePauseMenu();
                this.emit('reset');
            }
        });
        
        document.getElementById('main-menu-btn')?.addEventListener('click', () => {
            if (confirm('确定要返回主菜单吗？')) {
                this.hidePauseMenu();
                this.emit('backToMenu');
            }
        });
    }

    // 隐藏暂停菜单
    hidePauseMenu() {
        const pauseMenu = document.getElementById('pause-menu');
        if (pauseMenu) {
            pauseMenu.remove();
        }
    }

    // 获取当前设置
    getSettings() {
        return { ...this.settings };
    }

    // 应用设置
    applySettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.initializeSettings();
        this.emit('settingsChanged', this.settings);
    }

    // 清理资源
    destroy() {
        // 移除事件监听器
        document.removeEventListener('keydown', this.handleKeyPress);
        
        // 清理动态创建的元素
        this.hidePauseMenu();
        
        this.elements = {};
        this.isInitialized = false;
    }
}
