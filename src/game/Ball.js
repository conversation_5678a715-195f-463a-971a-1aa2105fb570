// 使用全局的THREE变量

export class Ball {
    constructor(type, number) {
        this.type = type; // 'cue', 'solid', 'stripe', 'eight'
        this.number = number; // 0 for cue ball, 1-15 for numbered balls
        
        this.mesh = null;
        this.body = null;
        this.scene = null;
        this.physics = null;
        
        // 球的物理属性
        this.radius = 0.057; // 标准台球半径 (57.15mm)
        this.mass = 0.16;    // 标准台球质量 (160g)
        
        // 状态
        this.isInPocket = false;
        this.isVisible = true;
        this.lastPosition = new THREE.Vector3();
        this.velocity = new THREE.Vector3();
        
        // 材质和颜色
        this.colors = this.getBallColors();
        
        this.isInitialized = false;
    }

    getBallColors() {
        // 标准台球颜色
        const colors = {
            0: 0xffffff,  // 白球
            1: 0xffff00,  // 黄色
            2: 0x0000ff,  // 蓝色
            3: 0xff0000,  // 红色
            4: 0x800080,  // 紫色
            5: 0xffa500,  // 橙色
            6: 0x008000,  // 绿色
            7: 0x800000,  // 栗色
            8: 0x000000,  // 黑色
            9: 0xffff00,  // 黄色条纹
            10: 0x0000ff, // 蓝色条纹
            11: 0xff0000, // 红色条纹
            12: 0x800080, // 紫色条纹
            13: 0xffa500, // 橙色条纹
            14: 0x008000, // 绿色条纹
            15: 0x800000  // 栗色条纹
        };
        
        return colors;
    }

    async init(scene, physics) {
        try {
            this.scene = scene;
            this.physics = physics;
            
            // 创建球的几何体
            await this.createGeometry();
            
            // 创建球的材质
            await this.createMaterial();
            
            // 创建球的网格
            this.createMesh();
            
            // 创建物理体
            this.createPhysicsBody();
            
            // 添加到场景
            this.scene.add(this.mesh);
            
            this.isInitialized = true;
            console.log(`${this.number}号球创建完成`);
            
        } catch (error) {
            console.error(`${this.number}号球创建失败:`, error);
            throw error;
        }
    }

    async createGeometry() {
        // 创建球体几何体
        this.geometry = new THREE.SphereGeometry(this.radius, 32, 16);
    }

    async createMaterial() {
        const baseColor = this.colors[this.number] || 0xffffff;
        
        if (this.type === 'cue') {
            // 白球材质
            this.material = new THREE.MeshPhongMaterial({
                color: baseColor,
                shininess: 100,
                specular: 0x222222,
                transparent: false
            });
        } else if (this.type === 'eight') {
            // 8号球特殊材质
            this.material = new THREE.MeshPhongMaterial({
                color: baseColor,
                shininess: 100,
                specular: 0x222222
            });
        } else if (this.type === 'solid') {
            // 实心球材质
            this.material = new THREE.MeshPhongMaterial({
                color: baseColor,
                shininess: 80,
                specular: 0x111111
            });
        } else if (this.type === 'stripe') {
            // 花球材质 - 创建条纹效果
            this.material = await this.createStripeMaterial(baseColor);
        }
        
        // 添加数字纹理
        if (this.number > 0) {
            await this.addNumberTexture();
        }
    }

    async createStripeMaterial(baseColor) {
        // 创建条纹纹理
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const context = canvas.getContext('2d');
        
        // 绘制白色背景
        context.fillStyle = '#ffffff';
        context.fillRect(0, 0, 256, 256);
        
        // 绘制彩色条纹
        context.fillStyle = `#${baseColor.toString(16).padStart(6, '0')}`;
        const stripeWidth = 32;
        for (let i = 0; i < 256; i += stripeWidth * 2) {
            context.fillRect(i, 0, stripeWidth, 256);
        }
        
        // 创建纹理
        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(4, 2);
        
        return new THREE.MeshPhongMaterial({
            map: texture,
            shininess: 80,
            specular: 0x111111
        });
    }

    async addNumberTexture() {
        // 创建数字纹理
        const canvas = document.createElement('canvas');
        canvas.width = 128;
        canvas.height = 128;
        const context = canvas.getContext('2d');
        
        // 绘制白色圆形背景
        context.fillStyle = '#ffffff';
        context.beginPath();
        context.arc(64, 64, 50, 0, Math.PI * 2);
        context.fill();
        
        // 绘制数字
        context.fillStyle = '#000000';
        context.font = 'bold 48px Arial';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText(this.number.toString(), 64, 64);
        
        // 创建纹理
        const numberTexture = new THREE.CanvasTexture(canvas);
        
        // 如果已有材质，创建组合材质
        if (this.material) {
            // 创建数字贴花
            const numberGeometry = new THREE.CircleGeometry(this.radius * 0.3, 16);
            const numberMaterial = new THREE.MeshBasicMaterial({
                map: numberTexture,
                transparent: true,
                alphaTest: 0.1
            });
            
            this.numberDecal = new THREE.Mesh(numberGeometry, numberMaterial);
            this.numberDecal.position.set(0, 0, this.radius + 0.001);
        }
    }

    createMesh() {
        this.mesh = new THREE.Mesh(this.geometry, this.material);
        this.mesh.castShadow = true;
        this.mesh.receiveShadow = true;
        this.mesh.userData = {
            type: 'ball',
            ballType: this.type,
            number: this.number,
            ballInstance: this
        };
        
        // 添加数字贴花
        if (this.numberDecal) {
            this.mesh.add(this.numberDecal);
        }
    }

    createPhysicsBody() {
        if (!this.physics) return;
        
        this.body = this.physics.createBallBody(this.radius, this.mass);
        this.body.userData = {
            type: 'ball',
            ballInstance: this
        };
    }

    // 设置球的位置
    setPosition(x, y, z) {
        const position = new THREE.Vector3(x, y, z);
        
        if (this.mesh) {
            this.mesh.position.copy(position);
        }
        
        if (this.body && this.physics) {
            this.physics.setBodyPosition(this.body, position);
        }
        
        this.lastPosition.copy(position);
    }

    // 获取球的位置
    getPosition() {
        if (this.body && this.physics) {
            const pos = this.physics.getBodyPosition(this.body);
            return new THREE.Vector3(pos.x, pos.y, pos.z);
        } else if (this.mesh) {
            return this.mesh.position.clone();
        }
        return new THREE.Vector3();
    }

    // 应用力到球
    applyForce(direction, magnitude) {
        if (!this.body || !this.physics) return;
        
        const force = direction.clone().multiplyScalar(magnitude);
        this.physics.applyImpulse(this.body, force);
        
        // 唤醒物理体
        this.physics.wakeUpBody(this.body);
    }

    // 应用旋转
    applySpin(spinVector) {
        if (!this.body) return;
        
        this.body.angularVelocity.set(spinVector.x, spinVector.y, spinVector.z);
    }

    // 停止球的运动
    stop() {
        if (this.body) {
            this.body.velocity.set(0, 0, 0);
            this.body.angularVelocity.set(0, 0, 0);
            this.physics.sleepBody(this.body);
        }
    }

    // 检查球是否静止
    isStopped() {
        if (this.body && this.physics) {
            return this.physics.isBodySleeping(this.body);
        }
        return true;
    }

    // 获取球的速度
    getVelocity() {
        if (this.body) {
            return new THREE.Vector3(
                this.body.velocity.x,
                this.body.velocity.y,
                this.body.velocity.z
            );
        }
        return new THREE.Vector3();
    }

    // 获取球的角速度
    getAngularVelocity() {
        if (this.body) {
            return new THREE.Vector3(
                this.body.angularVelocity.x,
                this.body.angularVelocity.y,
                this.body.angularVelocity.z
            );
        }
        return new THREE.Vector3();
    }

    // 设置球的可见性
    setVisible(visible) {
        this.isVisible = visible;
        if (this.mesh) {
            this.mesh.visible = visible;
        }
    }

    // 进袋处理
    enterPocket() {
        this.isInPocket = true;
        this.setVisible(false);
        
        // 移除物理体
        if (this.body && this.physics) {
            this.physics.removeBody(this.body);
            this.body = null;
        }
    }

    // 从袋中取出
    exitPocket() {
        this.isInPocket = false;
        this.setVisible(true);
        
        // 重新创建物理体
        this.createPhysicsBody();
    }

    // 重置球的状态
    reset() {
        this.isInPocket = false;
        this.setVisible(true);
        this.stop();
        
        // 如果物理体被移除，重新创建
        if (!this.body) {
            this.createPhysicsBody();
        }
    }

    // 更新球的状态
    update() {
        if (!this.isInitialized || this.isInPocket) return;
        
        // 同步物理体和渲染网格的位置
        if (this.body && this.mesh && this.physics) {
            const position = this.physics.getBodyPosition(this.body);
            const rotation = this.physics.getBodyRotation(this.body);
            
            this.mesh.position.set(position.x, position.y, position.z);
            this.mesh.quaternion.set(rotation.x, rotation.y, rotation.z, rotation.w);
            
            // 更新速度
            this.velocity = this.getVelocity();
        }
    }

    // 获取球的信息
    getInfo() {
        return {
            number: this.number,
            type: this.type,
            position: this.getPosition(),
            velocity: this.getVelocity(),
            angularVelocity: this.getAngularVelocity(),
            isInPocket: this.isInPocket,
            isVisible: this.isVisible,
            isStopped: this.isStopped()
        };
    }

    // 计算与另一个球的距离
    distanceTo(otherBall) {
        const pos1 = this.getPosition();
        const pos2 = otherBall.getPosition();
        return pos1.distanceTo(pos2);
    }

    // 检查是否与另一个球碰撞
    isCollidingWith(otherBall) {
        const distance = this.distanceTo(otherBall);
        return distance <= (this.radius + otherBall.radius);
    }

    // 预测球的轨迹
    predictTrajectory(steps = 100, timeStep = 0.1) {
        if (!this.body) return [];
        
        const trajectory = [];
        const currentVel = this.getVelocity();
        const currentPos = this.getPosition();
        
        let pos = currentPos.clone();
        let vel = currentVel.clone();
        
        for (let i = 0; i < steps; i++) {
            // 简单的物理预测（忽略复杂碰撞）
            pos.add(vel.clone().multiplyScalar(timeStep));
            vel.multiplyScalar(0.98); // 简单的摩擦力
            
            trajectory.push(pos.clone());
            
            // 如果速度太小，停止预测
            if (vel.length() < 0.01) break;
        }
        
        return trajectory;
    }

    // 清理资源
    destroy() {
        // 从场景中移除
        if (this.mesh && this.scene) {
            this.scene.remove(this.mesh);
        }
        
        // 清理几何体和材质
        if (this.geometry) {
            this.geometry.dispose();
        }
        
        if (this.material) {
            if (this.material.map) {
                this.material.map.dispose();
            }
            this.material.dispose();
        }
        
        if (this.numberDecal) {
            this.numberDecal.geometry.dispose();
            this.numberDecal.material.dispose();
        }
        
        // 移除物理体
        if (this.body && this.physics) {
            this.physics.removeBody(this.body);
        }
        
        this.mesh = null;
        this.body = null;
        this.geometry = null;
        this.material = null;
        this.numberDecal = null;
        this.scene = null;
        this.physics = null;
        this.isInitialized = false;
    }
}
