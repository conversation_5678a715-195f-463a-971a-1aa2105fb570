export class GameRules {
    constructor(mode = 'single') {
        this.mode = mode; // 'single' or 'two'
        this.balls = [];
        this.gameState = 'break'; // 'break', 'open', 'assigned'
        
        // 玩家状态
        this.players = {
            1: { group: null, score: 0, fouls: 0 },
            2: { group: null, score: 0, fouls: 0 }
        };
        
        // 当前回合信息
        this.currentTurn = {
            player: 1,
            ballsPocketed: [],
            firstBallHit: null,
            cueBallPocketed: false,
            validShot: true,
            foulCommitted: false,
            foulReason: ''
        };
        
        // 游戏历史
        this.turnHistory = [];
        this.gameStartTime = null;
        
        this.isInitialized = false;
    }

    init(balls) {
        this.balls = balls;
        this.gameStartTime = Date.now();
        this.resetTurn();
        this.isInitialized = true;
        console.log('游戏规则初始化完成');
    }

    // 检查回合结果
    checkTurn(balls, currentPlayer) {
        this.currentTurn.player = currentPlayer;
        
        // 检查进袋的球
        this.checkPocketedBalls(balls);
        
        // 检查犯规
        this.checkFouls(balls);
        
        // 检查游戏是否结束
        const gameOverResult = this.checkGameOver();
        if (gameOverResult.gameOver) {
            return gameOverResult;
        }
        
        // 确定是否换人
        const shouldSwitch = this.shouldSwitchPlayer();
        
        // 记录回合历史
        this.recordTurn();
        
        // 重置回合状态
        this.resetTurn();
        
        return {
            gameOver: false,
            switchPlayer: shouldSwitch,
            foul: this.currentTurn.foulCommitted,
            message: this.currentTurn.foulReason
        };
    }

    checkPocketedBalls(balls) {
        this.currentTurn.ballsPocketed = [];
        
        balls.forEach(ball => {
            if (ball.isInPocket && !this.wasBallPocketedBefore(ball.number)) {
                this.currentTurn.ballsPocketed.push(ball.number);
                
                // 更新玩家分数
                if (ball.number !== 0 && ball.number !== 8) {
                    this.players[this.currentTurn.player].score++;
                }
            }
        });
        
        // 检查白球是否进袋
        const cueBall = balls[0];
        if (cueBall.isInPocket) {
            this.currentTurn.cueBallPocketed = true;
        }
    }

    wasBallPocketedBefore(ballNumber) {
        // 检查球是否在之前的回合中已经进袋
        return this.turnHistory.some(turn => 
            turn.ballsPocketed.includes(ballNumber)
        );
    }

    checkFouls(balls) {
        this.currentTurn.foulCommitted = false;
        this.currentTurn.foulReason = '';
        
        // 1. 白球进袋
        if (this.currentTurn.cueBallPocketed) {
            this.commitFoul('白球进袋');
            return;
        }
        
        // 2. 没有击中任何球
        if (!this.currentTurn.firstBallHit) {
            this.commitFoul('没有击中任何球');
            return;
        }
        
        // 3. 开球阶段的特殊规则
        if (this.gameState === 'break') {
            this.checkBreakFouls();
            return;
        }
        
        // 4. 击中错误的球
        if (this.gameState === 'assigned') {
            this.checkWrongBallHit();
        }
        
        // 5. 没有球进袋且没有球碰到边框
        if (this.currentTurn.ballsPocketed.length === 0) {
            if (!this.checkBallHitCushion(balls)) {
                this.commitFoul('击球后没有球进袋且没有球碰到边框');
            }
        }
        
        // 6. 8号球犯规
        this.check8BallFouls();
    }

    checkBreakFouls() {
        // 开球必须至少有4个球碰到边框或有球进袋
        const pocketedCount = this.currentTurn.ballsPocketed.length;
        
        if (pocketedCount === 0) {
            // 简化规则：开球时只要击中球堆即可
            if (!this.currentTurn.firstBallHit || this.currentTurn.firstBallHit === 0) {
                this.commitFoul('开球犯规：没有有效击中球堆');
            }
        }
        
        // 如果8号球在开球时进袋
        if (this.currentTurn.ballsPocketed.includes(8)) {
            if (this.currentTurn.cueBallPocketed) {
                this.commitFoul('开球时8号球和白球同时进袋');
            } else {
                // 8号球进袋但白球没进袋，重新摆球
                this.gameState = 'break';
            }
        }
    }

    checkWrongBallHit() {
        const player = this.players[this.currentTurn.player];
        const firstHit = this.currentTurn.firstBallHit;
        
        if (!player.group) return; // 还没分组
        
        // 检查是否先击中了自己的目标球
        const isTargetBall = this.isBallInGroup(firstHit, player.group);
        
        if (!isTargetBall && firstHit !== 8) {
            this.commitFoul(`先击中了对方的球 (${firstHit}号)`);
        }
        
        // 如果所有目标球都进袋了，必须击中8号球
        if (this.areAllTargetBallsPocketed(this.currentTurn.player) && firstHit !== 8) {
            this.commitFoul('应该击中8号球');
        }
    }

    check8BallFouls() {
        const pocketed8Ball = this.currentTurn.ballsPocketed.includes(8);
        
        if (pocketed8Ball) {
            const player = this.players[this.currentTurn.player];
            
            // 如果还有目标球没进袋就击入8号球
            if (!this.areAllTargetBallsPocketed(this.currentTurn.player)) {
                this.commitFoul('过早击入8号球');
                return;
            }
            
            // 如果没有先击中8号球
            if (this.currentTurn.firstBallHit !== 8) {
                this.commitFoul('没有先击中8号球');
                return;
            }
        }
    }

    checkBallHitCushion(balls) {
        // 简化实现：假设总有球碰到边框
        // 在实际实现中，需要监听物理引擎的碰撞事件
        return true;
    }

    commitFoul(reason) {
        this.currentTurn.foulCommitted = true;
        this.currentTurn.foulReason = reason;
        this.currentTurn.validShot = false;
        this.players[this.currentTurn.player].fouls++;
    }

    shouldSwitchPlayer() {
        // 犯规时换人
        if (this.currentTurn.foulCommitted) {
            return true;
        }
        
        // 没有进球时换人
        if (this.currentTurn.ballsPocketed.length === 0) {
            return true;
        }
        
        // 进了对方的球时换人
        const player = this.players[this.currentTurn.player];
        if (player.group && this.currentTurn.ballsPocketed.length > 0) {
            const pocketedOwnBalls = this.currentTurn.ballsPocketed.filter(ballNum => 
                this.isBallInGroup(ballNum, player.group)
            );
            
            if (pocketedOwnBalls.length === 0 && !this.currentTurn.ballsPocketed.includes(8)) {
                return true;
            }
        }
        
        return false;
    }

    checkGameOver() {
        const pocketed8Ball = this.currentTurn.ballsPocketed.includes(8);
        
        if (pocketed8Ball) {
            if (this.currentTurn.foulCommitted) {
                // 8号球犯规，对手获胜
                const winner = this.currentTurn.player === 1 ? 2 : 1;
                return { gameOver: true, winner, reason: '8号球犯规' };
            } else if (this.areAllTargetBallsPocketed(this.currentTurn.player)) {
                // 正常击入8号球，当前玩家获胜
                return { gameOver: true, winner: this.currentTurn.player, reason: '正常击入8号球' };
            }
        }
        
        return { gameOver: false };
    }

    areAllTargetBallsPocketed(player) {
        const playerGroup = this.players[player].group;
        if (!playerGroup) return false;
        
        const targetBalls = this.getTargetBalls(playerGroup);
        return targetBalls.every(ballNum => this.isBallPocketed(ballNum));
    }

    isBallPocketed(ballNumber) {
        return this.turnHistory.some(turn => 
            turn.ballsPocketed.includes(ballNumber)
        ) || this.currentTurn.ballsPocketed.includes(ballNumber);
    }

    getTargetBalls(group) {
        if (group === 'solid') {
            return [1, 2, 3, 4, 5, 6, 7];
        } else if (group === 'stripe') {
            return [9, 10, 11, 12, 13, 14, 15];
        }
        return [];
    }

    isBallInGroup(ballNumber, group) {
        if (group === 'solid') {
            return ballNumber >= 1 && ballNumber <= 7;
        } else if (group === 'stripe') {
            return ballNumber >= 9 && ballNumber <= 15;
        }
        return false;
    }

    assignGroups() {
        if (this.gameState !== 'open') return;
        
        const pocketed = this.currentTurn.ballsPocketed;
        if (pocketed.length === 0) return;
        
        const solidPocketed = pocketed.some(ball => ball >= 1 && ball <= 7);
        const stripePocketed = pocketed.some(ball => ball >= 9 && ball <= 15);
        
        if (solidPocketed && !stripePocketed) {
            this.players[this.currentTurn.player].group = 'solid';
            this.players[this.currentTurn.player === 1 ? 2 : 1].group = 'stripe';
            this.gameState = 'assigned';
        } else if (stripePocketed && !solidPocketed) {
            this.players[this.currentTurn.player].group = 'stripe';
            this.players[this.currentTurn.player === 1 ? 2 : 1].group = 'solid';
            this.gameState = 'assigned';
        }
    }

    recordTurn() {
        this.turnHistory.push({
            player: this.currentTurn.player,
            ballsPocketed: [...this.currentTurn.ballsPocketed],
            firstBallHit: this.currentTurn.firstBallHit,
            cueBallPocketed: this.currentTurn.cueBallPocketed,
            foulCommitted: this.currentTurn.foulCommitted,
            foulReason: this.currentTurn.foulReason,
            timestamp: Date.now()
        });
    }

    resetTurn() {
        this.currentTurn = {
            player: this.currentTurn.player,
            ballsPocketed: [],
            firstBallHit: null,
            cueBallPocketed: false,
            validShot: true,
            foulCommitted: false,
            foulReason: ''
        };
    }

    // 获取当前分数
    getScores() {
        return {
            player1: this.players[1].score,
            player2: this.players[2].score
        };
    }

    // 获取玩家组别
    getPlayerGroups() {
        return {
            player1: this.players[1].group,
            player2: this.players[2].group
        };
    }

    // 获取游戏状态
    getGameState() {
        return {
            state: this.gameState,
            currentPlayer: this.currentTurn.player,
            players: this.players,
            turnHistory: this.turnHistory,
            gameTime: Date.now() - this.gameStartTime
        };
    }

    // 重置游戏
    reset() {
        this.gameState = 'break';
        this.players = {
            1: { group: null, score: 0, fouls: 0 },
            2: { group: null, score: 0, fouls: 0 }
        };
        this.turnHistory = [];
        this.gameStartTime = Date.now();
        this.resetTurn();
        this.currentTurn.player = 1;
    }

    // 设置第一个击中的球（由物理引擎调用）
    setFirstBallHit(ballNumber) {
        if (!this.currentTurn.firstBallHit) {
            this.currentTurn.firstBallHit = ballNumber;
        }
    }

    // 获取规则说明
    getRulesDescription() {
        return {
            title: "8球台球规则",
            rules: [
                "游戏目标：先击入自己的目标球组（实心球1-7或花球9-15），然后击入8号球获胜",
                "开球：必须击中球堆，至少4个球碰边框或有球进袋",
                "分组：第一次进球确定目标球组",
                "击球：必须先击中自己的目标球",
                "犯规：白球进袋、没击中目标球、过早击入8号球等",
                "获胜：击入所有目标球后正确击入8号球"
            ]
        };
    }
}
