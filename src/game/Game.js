// 使用全局的THREE变量
import { Table } from './Table.js';
import { Ball } from './Ball.js';
import { Cue } from './Cue.js';
import { Physics } from './Physics.js';
import { GameRules } from './GameRules.js';
import { EventEmitter } from '../utils/EventEmitter.js';

export class Game extends EventEmitter {
    constructor(mode = 'single') {
        super();
        
        this.mode = mode; // 'single' or 'two'
        this.isInitialized = false;
        this.isPaused = false;
        this.isGameOver = false;
        
        // Three.js 核心对象
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.canvas = null;
        
        // 游戏对象
        this.table = null;
        this.balls = [];
        this.cue = null;
        this.physics = null;
        this.rules = null;
        
        // 游戏状态
        this.currentPlayer = 1;
        this.scores = { player1: 0, player2: 0 };
        this.power = 0;
        this.isAiming = false;
        this.isShooting = false;
        
        // 控制相关
        this.mouse = new THREE.Vector2();
        this.raycaster = new THREE.Raycaster();
        this.controls = null;
        
        // 设置
        this.settings = {
            soundVolume: 50,
            graphicsQuality: 'medium',
            showTrajectory: true
        };
        
        // 动画
        this.animationId = null;
        this.clock = new THREE.Clock();
    }

    async init() {
        try {
            // 获取canvas元素
            this.canvas = document.getElementById('game-canvas');
            if (!this.canvas) {
                throw new Error('找不到游戏canvas元素');
            }

            // 初始化Three.js
            this.initThreeJS();
            
            // 初始化物理引擎
            this.physics = new Physics();
            await this.physics.init();
            
            // 创建台球桌
            this.table = new Table();
            await this.table.init(this.scene, this.physics);
            
            // 创建台球
            await this.createBalls();
            
            // 创建球杆
            this.cue = new Cue();
            await this.cue.init(this.scene);
            
            // 初始化游戏规则
            this.rules = new GameRules(this.mode);
            this.rules.init(this.balls);
            
            // 设置事件监听
            this.setupEventListeners();
            
            // 开始游戏循环
            this.startGameLoop();
            
            this.isInitialized = true;
            console.log('游戏初始化完成');
            
        } catch (error) {
            console.error('游戏初始化失败:', error);
            throw error;
        }
    }

    initThreeJS() {
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x1a1a1a);
        
        // 创建相机
        const aspect = window.innerWidth / window.innerHeight;
        this.camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);
        this.camera.position.set(0, 8, 12);
        this.camera.lookAt(0, 0, 0);
        
        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: this.canvas,
            antialias: this.settings.graphicsQuality !== 'low'
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.physicallyCorrectLights = true;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1;
        
        // 添加光照
        this.setupLighting();
    }

    setupLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(ambientLight);
        
        // 主光源
        const mainLight = new THREE.DirectionalLight(0xffffff, 1);
        mainLight.position.set(10, 20, 10);
        mainLight.castShadow = true;
        mainLight.shadow.mapSize.width = 2048;
        mainLight.shadow.mapSize.height = 2048;
        mainLight.shadow.camera.near = 0.5;
        mainLight.shadow.camera.far = 50;
        mainLight.shadow.camera.left = -20;
        mainLight.shadow.camera.right = 20;
        mainLight.shadow.camera.top = 20;
        mainLight.shadow.camera.bottom = -20;
        this.scene.add(mainLight);
        
        // 补充光源
        const fillLight = new THREE.DirectionalLight(0x4080ff, 0.3);
        fillLight.position.set(-10, 10, -10);
        this.scene.add(fillLight);
        
        // 台球桌上方的聚光灯
        const spotLight = new THREE.SpotLight(0xffffff, 0.8);
        spotLight.position.set(0, 15, 0);
        spotLight.target.position.set(0, 0, 0);
        spotLight.angle = Math.PI / 3;
        spotLight.penumbra = 0.1;
        spotLight.decay = 2;
        spotLight.distance = 30;
        spotLight.castShadow = true;
        this.scene.add(spotLight);
        this.scene.add(spotLight.target);
    }

    async createBalls() {
        // 创建白球（母球）
        const cueBall = new Ball('cue', 0);
        await cueBall.init(this.scene, this.physics);
        cueBall.setPosition(-6, 0, 0);
        this.balls.push(cueBall);
        
        // 创建彩球（1-15号球）
        const ballPositions = this.getBallRackPositions();
        
        for (let i = 1; i <= 15; i++) {
            const ballType = i <= 7 ? 'solid' : (i === 8 ? 'eight' : 'stripe');
            const ball = new Ball(ballType, i);
            await ball.init(this.scene, this.physics);
            ball.setPosition(ballPositions[i - 1].x, ballPositions[i - 1].y, ballPositions[i - 1].z);
            this.balls.push(ball);
        }
    }

    getBallRackPositions() {
        // 标准的15球三角排列
        const positions = [];
        const startX = 6;
        const startZ = 0;
        const ballRadius = 0.057; // 标准台球半径
        const spacing = ballRadius * 2.1;
        
        // 第一排（1个球）
        positions.push({ x: startX, y: ballRadius, z: startZ });
        
        // 第二排（2个球）
        positions.push({ x: startX + spacing, y: ballRadius, z: startZ - spacing / 2 });
        positions.push({ x: startX + spacing, y: ballRadius, z: startZ + spacing / 2 });
        
        // 第三排（3个球）
        positions.push({ x: startX + spacing * 2, y: ballRadius, z: startZ - spacing });
        positions.push({ x: startX + spacing * 2, y: ballRadius, z: startZ });
        positions.push({ x: startX + spacing * 2, y: ballRadius, z: startZ + spacing });
        
        // 第四排（4个球）
        positions.push({ x: startX + spacing * 3, y: ballRadius, z: startZ - spacing * 1.5 });
        positions.push({ x: startX + spacing * 3, y: ballRadius, z: startZ - spacing / 2 });
        positions.push({ x: startX + spacing * 3, y: ballRadius, z: startZ + spacing / 2 });
        positions.push({ x: startX + spacing * 3, y: ballRadius, z: startZ + spacing * 1.5 });
        
        // 第五排（5个球）
        positions.push({ x: startX + spacing * 4, y: ballRadius, z: startZ - spacing * 2 });
        positions.push({ x: startX + spacing * 4, y: ballRadius, z: startZ - spacing });
        positions.push({ x: startX + spacing * 4, y: ballRadius, z: startZ });
        positions.push({ x: startX + spacing * 4, y: ballRadius, z: startZ + spacing });
        positions.push({ x: startX + spacing * 4, y: ballRadius, z: startZ + spacing * 2 });
        
        return positions;
    }

    setupEventListeners() {
        // 鼠标事件
        this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
        this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
        this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
        this.canvas.addEventListener('wheel', this.onMouseWheel.bind(this));
        
        // 键盘事件
        window.addEventListener('keydown', this.onKeyDown.bind(this));
        
        // 窗口大小变化
        window.addEventListener('resize', this.handleResize.bind(this));
    }

    onMouseMove(event) {
        if (!this.isInitialized || this.isPaused || this.isGameOver) return;
        
        // 更新鼠标位置
        const rect = this.canvas.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
        
        // 如果正在瞄准，更新球杆方向
        if (this.isAiming && this.cue) {
            this.updateCueDirection();
        }
    }

    onMouseDown(event) {
        if (!this.isInitialized || this.isPaused || this.isGameOver) return;
        
        if (event.button === 0) { // 左键
            this.startAiming();
        }
    }

    onMouseUp(event) {
        if (!this.isInitialized || this.isPaused || this.isGameOver) return;
        
        if (event.button === 0 && this.isAiming) { // 左键
            this.shoot();
        }
    }

    onMouseWheel(event) {
        if (!this.isInitialized) return;
        
        // 调整相机距离
        const delta = event.deltaY * 0.01;
        const direction = new THREE.Vector3();
        this.camera.getWorldDirection(direction);
        this.camera.position.addScaledVector(direction, delta);
    }

    onKeyDown(event) {
        if (!this.isInitialized) return;
        
        switch (event.code) {
            case 'Space':
                event.preventDefault();
                if (this.isPaused) {
                    this.resume();
                } else {
                    this.pause();
                }
                break;
            case 'KeyR':
                this.reset();
                break;
        }
    }

    startAiming() {
        if (this.isShooting || !this.canShoot()) return;
        
        this.isAiming = true;
        this.power = 0;
        
        // 显示球杆
        if (this.cue) {
            this.cue.show();
            this.updateCueDirection();
        }
        
        // 开始力度积累
        this.startPowerAccumulation();
    }

    updateCueDirection() {
        // 使用射线检测确定瞄准方向
        this.raycaster.setFromCamera(this.mouse, this.camera);
        
        // 创建一个平面来计算瞄准点
        const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0);
        const intersectPoint = new THREE.Vector3();
        this.raycaster.ray.intersectPlane(plane, intersectPoint);
        
        // 获取白球位置
        const cueBall = this.balls[0];
        const cueBallPos = cueBall.getPosition();
        
        // 计算方向
        const direction = new THREE.Vector3()
            .subVectors(intersectPoint, cueBallPos)
            .normalize();
        
        // 更新球杆位置和方向
        if (this.cue) {
            this.cue.updateDirection(cueBallPos, direction);
        }
    }

    startPowerAccumulation() {
        const startTime = Date.now();
        const maxPower = 100;
        const accumTime = 2000; // 2秒达到最大力度
        
        const updatePower = () => {
            if (!this.isAiming) return;
            
            const elapsed = Date.now() - startTime;
            this.power = Math.min((elapsed / accumTime) * maxPower, maxPower);
            
            // 发送力度更新事件
            this.emit('powerChange', this.power);
            
            // 更新球杆位置（根据力度后退）
            if (this.cue) {
                this.cue.updatePower(this.power / maxPower);
            }
            
            if (this.isAiming) {
                requestAnimationFrame(updatePower);
            }
        };
        
        updatePower();
    }

    shoot() {
        if (!this.isAiming || this.power === 0) return;
        
        this.isAiming = false;
        this.isShooting = true;
        
        // 隐藏球杆
        if (this.cue) {
            this.cue.hide();
        }
        
        // 计算击球力度和方向
        const cueBall = this.balls[0];
        const direction = this.cue.getDirection();
        const force = (this.power / 100) * 15; // 最大力度15
        
        // 应用力到白球
        cueBall.applyForce(direction, force);
        
        // 重置力度
        this.power = 0;
        this.emit('powerChange', this.power);
        
        // 等待球停止后检查结果
        this.waitForBallsToStop();
    }

    canShoot() {
        // 检查是否所有球都已停止
        return this.balls.every(ball => ball.isStopped());
    }

    waitForBallsToStop() {
        const checkInterval = setInterval(() => {
            if (this.canShoot()) {
                clearInterval(checkInterval);
                this.isShooting = false;
                this.onTurnEnd();
            }
        }, 100);
    }

    onTurnEnd() {
        // 检查游戏规则
        const result = this.rules.checkTurn(this.balls, this.currentPlayer);
        
        if (result.gameOver) {
            this.endGame(result.winner);
            return;
        }
        
        if (result.foul) {
            this.emit('foul', result.message);
        }
        
        if (result.switchPlayer) {
            this.switchPlayer();
        }
        
        // 更新分数
        this.updateScores();
    }

    switchPlayer() {
        this.currentPlayer = this.currentPlayer === 1 ? 2 : 1;
        this.emit('playerChange', this.currentPlayer);
    }

    updateScores() {
        // 根据游戏规则计算分数
        const scores = this.rules.getScores();
        this.scores = scores;
        this.emit('scoreUpdate', this.scores);
    }

    endGame(winner) {
        this.isGameOver = true;
        this.emit('gameOver', winner);
    }

    startGameLoop() {
        const animate = () => {
            if (!this.isInitialized) return;
            
            this.animationId = requestAnimationFrame(animate);
            
            if (!this.isPaused) {
                const deltaTime = this.clock.getDelta();
                
                // 更新物理
                if (this.physics) {
                    this.physics.update(deltaTime);
                }
                
                // 更新球的位置
                this.balls.forEach(ball => {
                    ball.update();
                });
                
                // 更新球杆
                if (this.cue) {
                    this.cue.update(deltaTime);
                }
            }
            
            // 渲染场景
            this.renderer.render(this.scene, this.camera);
        };
        
        animate();
    }

    pause() {
        this.isPaused = true;
        this.emit('pause');
    }

    resume() {
        this.isPaused = false;
        this.emit('resume');
    }

    reset() {
        // 重置游戏状态
        this.currentPlayer = 1;
        this.scores = { player1: 0, player2: 0 };
        this.power = 0;
        this.isAiming = false;
        this.isShooting = false;
        this.isGameOver = false;
        
        // 重置球的位置
        this.resetBallPositions();
        
        // 重置游戏规则
        if (this.rules) {
            this.rules.reset();
        }
        
        // 发送重置事件
        this.emit('reset');
        this.emit('playerChange', this.currentPlayer);
        this.emit('scoreUpdate', this.scores);
        this.emit('powerChange', this.power);
    }

    resetBallPositions() {
        // 重置白球位置
        this.balls[0].setPosition(-6, 0, 0);
        this.balls[0].stop();
        
        // 重置彩球位置
        const ballPositions = this.getBallRackPositions();
        for (let i = 1; i < this.balls.length; i++) {
            const pos = ballPositions[i - 1];
            this.balls[i].setPosition(pos.x, pos.y, pos.z);
            this.balls[i].stop();
        }
    }

    updateSettings(settings) {
        this.settings = { ...this.settings, ...settings };
        
        // 应用图形质量设置
        if (settings.graphicsQuality) {
            this.updateGraphicsQuality(settings.graphicsQuality);
        }
    }

    updateGraphicsQuality(quality) {
        switch (quality) {
            case 'low':
                this.renderer.shadowMap.enabled = false;
                this.renderer.antialias = false;
                break;
            case 'medium':
                this.renderer.shadowMap.enabled = true;
                this.renderer.antialias = false;
                break;
            case 'high':
                this.renderer.shadowMap.enabled = true;
                this.renderer.antialias = true;
                break;
        }
    }

    handleResize() {
        if (!this.camera || !this.renderer) return;
        
        const width = window.innerWidth;
        const height = window.innerHeight;
        
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        
        this.renderer.setSize(width, height);
    }

    destroy() {
        // 停止动画循环
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        // 清理事件监听
        if (this.canvas) {
            this.canvas.removeEventListener('mousemove', this.onMouseMove);
            this.canvas.removeEventListener('mousedown', this.onMouseDown);
            this.canvas.removeEventListener('mouseup', this.onMouseUp);
            this.canvas.removeEventListener('wheel', this.onMouseWheel);
        }
        
        window.removeEventListener('keydown', this.onKeyDown);
        window.removeEventListener('resize', this.handleResize);
        
        // 清理Three.js对象
        if (this.scene) {
            this.scene.clear();
        }
        
        if (this.renderer) {
            this.renderer.dispose();
        }
        
        // 清理游戏对象
        this.balls.forEach(ball => ball.destroy());
        this.balls = [];
        
        if (this.table) {
            this.table.destroy();
        }
        
        if (this.cue) {
            this.cue.destroy();
        }
        
        if (this.physics) {
            this.physics.destroy();
        }
        
        this.isInitialized = false;
    }
}
