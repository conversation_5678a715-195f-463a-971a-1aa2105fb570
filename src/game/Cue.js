// 使用全局的THREE变量

export class Cue {
    constructor() {
        this.mesh = null;
        this.scene = null;
        
        // 球杆属性
        this.length = 1.5;      // 球杆长度
        this.tipRadius = 0.006; // 杆头半径
        this.buttRadius = 0.015; // 杆尾半径
        
        // 状态
        this.isVisible = false;
        this.position = new THREE.Vector3();
        this.direction = new THREE.Vector3(1, 0, 0);
        this.power = 0;
        
        // 瞄准相关
        this.targetPosition = new THREE.Vector3();
        this.baseDistance = 0.2; // 基础距离
        this.maxDistance = 0.5;  // 最大后退距离
        
        // 动画
        this.animationMixer = null;
        this.shootAnimation = null;
        
        this.isInitialized = false;
    }

    async init(scene) {
        try {
            this.scene = scene;
            
            // 创建球杆几何体
            await this.createGeometry();
            
            // 创建球杆材质
            await this.createMaterial();
            
            // 创建球杆网格
            this.createMesh();
            
            // 创建瞄准辅助线
            await this.createAimingLine();
            
            // 初始隐藏球杆
            this.hide();
            
            this.isInitialized = true;
            console.log('球杆创建完成');
            
        } catch (error) {
            console.error('球杆创建失败:', error);
            throw error;
        }
    }

    async createGeometry() {
        // 创建球杆的圆锥几何体（从细到粗）
        this.geometry = new THREE.ConeGeometry(
            this.buttRadius,
            this.length,
            8,
            1,
            false
        );
        
        // 调整几何体，使杆头在前方
        this.geometry.translate(0, -this.length / 2, 0);
        this.geometry.rotateZ(Math.PI);
    }

    async createMaterial() {
        // 创建球杆材质 - 木质纹理
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 32;
        const context = canvas.getContext('2d');
        
        // 绘制木质纹理
        const gradient = context.createLinearGradient(0, 0, 256, 0);
        gradient.addColorStop(0, '#8B4513');    // 深棕色
        gradient.addColorStop(0.3, '#A0522D');  // 中棕色
        gradient.addColorStop(0.7, '#CD853F');  // 浅棕色
        gradient.addColorStop(1, '#DEB887');    // 很浅的棕色
        
        context.fillStyle = gradient;
        context.fillRect(0, 0, 256, 32);
        
        // 添加木纹效果
        context.strokeStyle = 'rgba(139, 69, 19, 0.3)';
        context.lineWidth = 1;
        for (let i = 0; i < 256; i += 8) {
            context.beginPath();
            context.moveTo(i, 0);
            context.lineTo(i + Math.random() * 4, 32);
            context.stroke();
        }
        
        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        
        this.material = new THREE.MeshPhongMaterial({
            map: texture,
            shininess: 30,
            specular: 0x111111
        });
        
        // 创建杆头材质（皮质）
        this.tipMaterial = new THREE.MeshPhongMaterial({
            color: 0x2F4F4F, // 深灰绿色
            shininess: 10,
            specular: 0x222222
        });
    }

    createMesh() {
        // 创建主球杆
        this.mesh = new THREE.Mesh(this.geometry, this.material);
        this.mesh.castShadow = true;
        this.mesh.receiveShadow = false;
        
        // 创建杆头
        const tipGeometry = new THREE.CylinderGeometry(
            this.tipRadius,
            this.tipRadius,
            0.02,
            8
        );
        
        this.tipMesh = new THREE.Mesh(tipGeometry, this.tipMaterial);
        this.tipMesh.position.set(0, this.length / 2, 0);
        this.tipMesh.castShadow = true;
        
        // 将杆头添加到主球杆
        this.mesh.add(this.tipMesh);
        
        // 添加到场景
        this.scene.add(this.mesh);
        
        // 设置用户数据
        this.mesh.userData = {
            type: 'cue',
            cueInstance: this
        };
    }

    async createAimingLine() {
        // 创建瞄准线
        const points = [];
        const lineLength = 2;
        const segments = 20;
        
        for (let i = 0; i <= segments; i++) {
            const t = i / segments;
            points.push(new THREE.Vector3(t * lineLength, 0, 0));
        }
        
        const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
        const lineMaterial = new THREE.LineDashedMaterial({
            color: 0xffffff,
            dashSize: 0.1,
            gapSize: 0.05,
            transparent: true,
            opacity: 0.8
        });
        
        this.aimingLine = new THREE.Line(lineGeometry, lineMaterial);
        this.aimingLine.computeLineDistances();
        this.aimingLine.visible = false;
        
        this.scene.add(this.aimingLine);
    }

    // 显示球杆
    show() {
        this.isVisible = true;
        if (this.mesh) {
            this.mesh.visible = true;
        }
        if (this.aimingLine) {
            this.aimingLine.visible = true;
        }
    }

    // 隐藏球杆
    hide() {
        this.isVisible = false;
        if (this.mesh) {
            this.mesh.visible = false;
        }
        if (this.aimingLine) {
            this.aimingLine.visible = false;
        }
    }

    // 更新球杆方向
    updateDirection(ballPosition, direction) {
        if (!this.isVisible) return;
        
        this.targetPosition.copy(ballPosition);
        this.direction.copy(direction).normalize();
        
        // 计算球杆位置（在球后方）
        const distance = this.baseDistance + (this.power / 100) * this.maxDistance;
        this.position.copy(ballPosition).addScaledVector(this.direction, -distance);
        
        // 更新球杆位置和旋转
        if (this.mesh) {
            this.mesh.position.copy(this.position);
            
            // 计算旋转
            const lookDirection = this.direction.clone();
            this.mesh.lookAt(
                this.position.x + lookDirection.x,
                this.position.y + lookDirection.y,
                this.position.z + lookDirection.z
            );
            
            // 调整旋转使杆头指向球
            this.mesh.rotateX(Math.PI / 2);
        }
        
        // 更新瞄准线
        this.updateAimingLine(ballPosition, direction);
    }

    updateAimingLine(ballPosition, direction) {
        if (!this.aimingLine) return;
        
        // 设置瞄准线位置和方向
        this.aimingLine.position.copy(ballPosition);
        this.aimingLine.lookAt(
            ballPosition.x + direction.x,
            ballPosition.y + direction.y,
            ballPosition.z + direction.z
        );
        
        // 调整瞄准线长度基于力度
        const scale = 1 + (this.power / 100) * 2;
        this.aimingLine.scale.setScalar(scale);
    }

    // 更新力度
    updatePower(powerRatio) {
        this.power = powerRatio * 100;
        
        // 根据力度调整球杆位置
        if (this.targetPosition && this.direction) {
            const distance = this.baseDistance + powerRatio * this.maxDistance;
            this.position.copy(this.targetPosition).addScaledVector(this.direction, -distance);
            
            if (this.mesh) {
                this.mesh.position.copy(this.position);
            }
        }
        
        // 更新瞄准线颜色基于力度
        if (this.aimingLine && this.aimingLine.material) {
            const color = new THREE.Color();
            color.setHSL(0.33 - powerRatio * 0.33, 1, 0.5); // 从绿色到红色
            this.aimingLine.material.color = color;
        }
    }

    // 获取球杆方向
    getDirection() {
        return this.direction.clone();
    }

    // 获取球杆位置
    getPosition() {
        return this.position.clone();
    }

    // 执行击球动画
    async playShootAnimation() {
        if (!this.mesh) return;
        
        return new Promise((resolve) => {
            const startPosition = this.position.clone();
            const endPosition = this.targetPosition.clone().addScaledVector(this.direction, -0.05);
            
            const duration = 200; // 毫秒
            const startTime = Date.now();
            
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // 使用缓动函数
                const easeProgress = this.easeOutQuad(progress);
                
                // 插值位置
                const currentPos = startPosition.clone().lerp(endPosition, easeProgress);
                this.mesh.position.copy(currentPos);
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    // 动画完成，恢复原位置
                    setTimeout(() => {
                        this.mesh.position.copy(startPosition);
                        resolve();
                    }, 100);
                }
            };
            
            animate();
        });
    }

    easeOutQuad(t) {
        return t * (2 - t);
    }

    // 创建轨迹预测线
    createTrajectoryLine(trajectory) {
        // 移除之前的轨迹线
        this.removeTrajectoryLine();
        
        if (trajectory.length < 2) return;
        
        const points = trajectory.map(point => new THREE.Vector3(point.x, point.y, point.z));
        const geometry = new THREE.BufferGeometry().setFromPoints(points);
        
        const material = new THREE.LineBasicMaterial({
            color: 0x00ff00,
            transparent: true,
            opacity: 0.6,
            linewidth: 2
        });
        
        this.trajectoryLine = new THREE.Line(geometry, material);
        this.scene.add(this.trajectoryLine);
    }

    removeTrajectoryLine() {
        if (this.trajectoryLine) {
            this.scene.remove(this.trajectoryLine);
            this.trajectoryLine.geometry.dispose();
            this.trajectoryLine.material.dispose();
            this.trajectoryLine = null;
        }
    }

    // 更新球杆状态
    update(deltaTime) {
        if (!this.isInitialized) return;
        
        // 这里可以添加球杆的动画更新逻辑
        // 比如轻微的摆动效果等
    }

    // 重置球杆状态
    reset() {
        this.power = 0;
        this.hide();
        this.removeTrajectoryLine();
    }

    // 获取球杆信息
    getInfo() {
        return {
            position: this.position.clone(),
            direction: this.direction.clone(),
            power: this.power,
            isVisible: this.isVisible,
            length: this.length
        };
    }

    // 清理资源
    destroy() {
        // 移除网格
        if (this.mesh && this.scene) {
            this.scene.remove(this.mesh);
        }
        
        // 移除瞄准线
        if (this.aimingLine && this.scene) {
            this.scene.remove(this.aimingLine);
        }
        
        // 移除轨迹线
        this.removeTrajectoryLine();
        
        // 清理几何体和材质
        if (this.geometry) {
            this.geometry.dispose();
        }
        
        if (this.material) {
            if (this.material.map) {
                this.material.map.dispose();
            }
            this.material.dispose();
        }
        
        if (this.tipMaterial) {
            this.tipMaterial.dispose();
        }
        
        if (this.aimingLine) {
            this.aimingLine.geometry.dispose();
            this.aimingLine.material.dispose();
        }
        
        this.mesh = null;
        this.tipMesh = null;
        this.aimingLine = null;
        this.geometry = null;
        this.material = null;
        this.tipMaterial = null;
        this.scene = null;
        this.isInitialized = false;
    }
}
