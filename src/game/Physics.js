// 使用全局的CANNON变量

export class Physics {
    constructor() {
        this.world = null;
        this.bodies = new Map(); // 存储物理体和对应的游戏对象
        this.isInitialized = false;
        
        // 物理参数
        this.gravity = -9.82;
        this.timeStep = 1 / 60;
        this.maxSubSteps = 3;
        
        // 材质
        this.materials = {};
    }

    async init() {
        try {
            // 创建物理世界
            this.world = new CANNON.World({
                gravity: new CANNON.Vec3(0, this.gravity, 0),
                broadphase: new CANNON.NaiveBroadphase(),
                solver: new CANNON.GSSolver()
            });

            // 设置世界参数
            this.world.solver.iterations = 10;
            this.world.solver.tolerance = 0.1;
            this.world.allowSleep = true;

            // 创建材质
            this.createMaterials();
            
            // 创建接触材质
            this.createContactMaterials();
            
            this.isInitialized = true;
            console.log('物理引擎初始化完成');
            
        } catch (error) {
            console.error('物理引擎初始化失败:', error);
            throw error;
        }
    }

    createMaterials() {
        // 台球材质
        this.materials.ball = new CANNON.Material('ball');
        this.materials.ball.friction = 0.1;
        this.materials.ball.restitution = 0.9;

        // 台球桌面材质
        this.materials.table = new CANNON.Material('table');
        this.materials.table.friction = 0.8;
        this.materials.table.restitution = 0.1;

        // 台球桌边框材质
        this.materials.cushion = new CANNON.Material('cushion');
        this.materials.cushion.friction = 0.2;
        this.materials.cushion.restitution = 0.8;

        // 球袋材质
        this.materials.pocket = new CANNON.Material('pocket');
        this.materials.pocket.friction = 0.9;
        this.materials.pocket.restitution = 0.1;
    }

    createContactMaterials() {
        // 球与球的碰撞
        const ballBallContact = new CANNON.ContactMaterial(
            this.materials.ball,
            this.materials.ball,
            {
                friction: 0.05,
                restitution: 0.95,
                contactEquationStiffness: 1e8,
                contactEquationRelaxation: 3
            }
        );
        this.world.addContactMaterial(ballBallContact);

        // 球与台面的碰撞
        const ballTableContact = new CANNON.ContactMaterial(
            this.materials.ball,
            this.materials.table,
            {
                friction: 0.4,
                restitution: 0.1,
                contactEquationStiffness: 1e8,
                contactEquationRelaxation: 3
            }
        );
        this.world.addContactMaterial(ballTableContact);

        // 球与边框的碰撞
        const ballCushionContact = new CANNON.ContactMaterial(
            this.materials.ball,
            this.materials.cushion,
            {
                friction: 0.2,
                restitution: 0.8,
                contactEquationStiffness: 1e8,
                contactEquationRelaxation: 3
            }
        );
        this.world.addContactMaterial(ballCushionContact);

        // 球与球袋的碰撞
        const ballPocketContact = new CANNON.ContactMaterial(
            this.materials.ball,
            this.materials.pocket,
            {
                friction: 0.9,
                restitution: 0.1,
                contactEquationStiffness: 1e6,
                contactEquationRelaxation: 10
            }
        );
        this.world.addContactMaterial(ballPocketContact);
    }

    // 创建球体物理体
    createBallBody(radius = 0.057, mass = 0.16) {
        const shape = new CANNON.Sphere(radius);
        const body = new CANNON.Body({
            mass: mass,
            material: this.materials.ball,
            shape: shape,
            linearDamping: 0.1,
            angularDamping: 0.1
        });

        // 设置睡眠参数
        body.sleepSpeedLimit = 0.1;
        body.sleepTimeLimit = 1;

        this.world.addBody(body);
        return body;
    }

    // 创建台球桌物理体
    createTableBody(width, height, length) {
        const bodies = [];

        // 台面
        const tableShape = new CANNON.Box(new CANNON.Vec3(width / 2, height / 2, length / 2));
        const tableBody = new CANNON.Body({
            mass: 0, // 静态物体
            material: this.materials.table,
            shape: tableShape,
            position: new CANNON.Vec3(0, -height / 2, 0)
        });
        this.world.addBody(tableBody);
        bodies.push(tableBody);

        return bodies;
    }

    // 创建边框物理体
    createCushionBodies(tableWidth, tableLength, cushionHeight = 0.1, cushionThickness = 0.1) {
        const bodies = [];
        const halfWidth = tableWidth / 2;
        const halfLength = tableLength / 2;

        // 长边边框
        const longCushionShape = new CANNON.Box(new CANNON.Vec3(halfLength, cushionHeight / 2, cushionThickness / 2));
        
        // 上边框
        const topCushion = new CANNON.Body({
            mass: 0,
            material: this.materials.cushion,
            shape: longCushionShape,
            position: new CANNON.Vec3(0, cushionHeight / 2, halfWidth + cushionThickness / 2)
        });
        this.world.addBody(topCushion);
        bodies.push(topCushion);

        // 下边框
        const bottomCushion = new CANNON.Body({
            mass: 0,
            material: this.materials.cushion,
            shape: longCushionShape,
            position: new CANNON.Vec3(0, cushionHeight / 2, -halfWidth - cushionThickness / 2)
        });
        this.world.addBody(bottomCushion);
        bodies.push(bottomCushion);

        // 短边边框
        const shortCushionShape = new CANNON.Box(new CANNON.Vec3(cushionThickness / 2, cushionHeight / 2, halfWidth));
        
        // 左边框
        const leftCushion = new CANNON.Body({
            mass: 0,
            material: this.materials.cushion,
            shape: shortCushionShape,
            position: new CANNON.Vec3(-halfLength - cushionThickness / 2, cushionHeight / 2, 0)
        });
        this.world.addBody(leftCushion);
        bodies.push(leftCushion);

        // 右边框
        const rightCushion = new CANNON.Body({
            mass: 0,
            material: this.materials.cushion,
            shape: shortCushionShape,
            position: new CANNON.Vec3(halfLength + cushionThickness / 2, cushionHeight / 2, 0)
        });
        this.world.addBody(rightCushion);
        bodies.push(rightCushion);

        return bodies;
    }

    // 创建球袋物理体
    createPocketBodies(tableWidth, tableLength, pocketRadius = 0.12) {
        const bodies = [];
        const halfWidth = tableWidth / 2;
        const halfLength = tableLength / 2;

        // 球袋位置
        const pocketPositions = [
            // 四个角袋
            { x: -halfLength, z: -halfWidth },
            { x: -halfLength, z: halfWidth },
            { x: halfLength, z: -halfWidth },
            { x: halfLength, z: halfWidth },
            // 两个中袋
            { x: 0, z: -halfWidth },
            { x: 0, z: halfWidth }
        ];

        pocketPositions.forEach(pos => {
            const pocketShape = new CANNON.Cylinder(pocketRadius, pocketRadius, 0.2, 8);
            const pocketBody = new CANNON.Body({
                mass: 0,
                material: this.materials.pocket,
                shape: pocketShape,
                position: new CANNON.Vec3(pos.x, -0.05, pos.z)
            });
            
            this.world.addBody(pocketBody);
            bodies.push(pocketBody);
        });

        return bodies;
    }

    // 应用力到物体
    applyForce(body, force, worldPoint) {
        if (!body || !force) return;
        
        const cannonForce = new CANNON.Vec3(force.x, force.y, force.z);
        const cannonPoint = worldPoint ? 
            new CANNON.Vec3(worldPoint.x, worldPoint.y, worldPoint.z) : 
            body.position;
            
        body.applyForce(cannonForce, cannonPoint);
    }

    // 应用冲量到物体
    applyImpulse(body, impulse, worldPoint) {
        if (!body || !impulse) return;
        
        const cannonImpulse = new CANNON.Vec3(impulse.x, impulse.y, impulse.z);
        const cannonPoint = worldPoint ? 
            new CANNON.Vec3(worldPoint.x, worldPoint.y, worldPoint.z) : 
            body.position;
            
        body.applyImpulse(cannonImpulse, cannonPoint);
    }

    // 设置物体位置
    setBodyPosition(body, position) {
        if (!body || !position) return;
        
        body.position.set(position.x, position.y, position.z);
        body.velocity.set(0, 0, 0);
        body.angularVelocity.set(0, 0, 0);
        body.wakeUp();
    }

    // 获取物体位置
    getBodyPosition(body) {
        if (!body) return null;
        
        return {
            x: body.position.x,
            y: body.position.y,
            z: body.position.z
        };
    }

    // 获取物体旋转
    getBodyRotation(body) {
        if (!body) return null;
        
        return {
            x: body.quaternion.x,
            y: body.quaternion.y,
            z: body.quaternion.z,
            w: body.quaternion.w
        };
    }

    // 检查物体是否静止
    isBodySleeping(body) {
        if (!body) return true;
        
        return body.sleepState === CANNON.Body.SLEEPING || 
               (body.velocity.length() < 0.01 && body.angularVelocity.length() < 0.01);
    }

    // 唤醒物体
    wakeUpBody(body) {
        if (body) {
            body.wakeUp();
        }
    }

    // 让物体进入睡眠状态
    sleepBody(body) {
        if (body) {
            body.sleep();
        }
    }

    // 移除物体
    removeBody(body) {
        if (body && this.world) {
            this.world.removeBody(body);
        }
    }

    // 检测碰撞
    checkCollision(bodyA, bodyB) {
        if (!bodyA || !bodyB) return false;
        
        // 简单的距离检测
        const distance = bodyA.position.distanceTo(bodyB.position);
        const minDistance = 0.114; // 两个球的半径之和
        
        return distance <= minDistance;
    }

    // 射线检测
    raycast(from, to, options = {}) {
        const result = new CANNON.RaycastResult();
        const ray = new CANNON.Ray(
            new CANNON.Vec3(from.x, from.y, from.z),
            new CANNON.Vec3(to.x, to.y, to.z)
        );
        
        this.world.raycastClosest(ray, options, result);
        
        if (result.hasHit) {
            return {
                hit: true,
                point: {
                    x: result.hitPointWorld.x,
                    y: result.hitPointWorld.y,
                    z: result.hitPointWorld.z
                },
                normal: {
                    x: result.hitNormalWorld.x,
                    y: result.hitNormalWorld.y,
                    z: result.hitNormalWorld.z
                },
                body: result.body,
                distance: result.distance
            };
        }
        
        return { hit: false };
    }

    // 更新物理世界
    update(deltaTime) {
        if (!this.world || !this.isInitialized) return;
        
        // 限制时间步长
        const clampedDeltaTime = Math.min(deltaTime, 0.1);
        
        try {
            this.world.step(this.timeStep, clampedDeltaTime, this.maxSubSteps);
        } catch (error) {
            console.error('物理更新错误:', error);
        }
    }

    // 获取世界信息
    getWorldInfo() {
        if (!this.world) return null;
        
        return {
            bodies: this.world.bodies.length,
            contacts: this.world.contacts.length,
            gravity: this.world.gravity,
            time: this.world.time
        };
    }

    // 清理资源
    destroy() {
        if (this.world) {
            // 移除所有物体
            while (this.world.bodies.length > 0) {
                this.world.removeBody(this.world.bodies[0]);
            }
            
            // 清理接触材质
            this.world.contactMaterials = [];
            
            this.world = null;
        }
        
        this.bodies.clear();
        this.materials = {};
        this.isInitialized = false;
    }
}
