<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D台球游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- 游戏容器 -->
    <div id="game-container">
        <canvas id="game-canvas"></canvas>
    </div>

    <!-- 游戏UI界面 -->
    <div id="game-ui">
        <!-- 主菜单 -->
        <div id="main-menu" class="menu-panel">
            <h1>3D台球游戏</h1>
            <div class="menu-buttons">
                <button id="single-player-btn" class="menu-btn">单人练习</button>
                <button id="two-player-btn" class="menu-btn">双人对战</button>
                <button id="settings-btn" class="menu-btn">游戏设置</button>
                <button id="help-btn" class="menu-btn">游戏帮助</button>
            </div>
        </div>

        <!-- 游戏中界面 -->
        <div id="game-hud" class="hidden">
            <!-- 得分板 -->
            <div id="scoreboard">
                <div class="player-score">
                    <span class="player-name">玩家1</span>
                    <span class="score" id="player1-score">0</span>
                </div>
                <div class="player-score">
                    <span class="player-name">玩家2</span>
                    <span class="score" id="player2-score">0</span>
                </div>
            </div>

            <!-- 当前玩家指示 -->
            <div id="current-player">
                <span id="current-player-name">玩家1</span> 的回合
            </div>

            <!-- 力度控制 -->
            <div id="power-control">
                <label>击球力度:</label>
                <div id="power-bar">
                    <div id="power-fill"></div>
                </div>
                <span id="power-value">0%</span>
            </div>

            <!-- 游戏控制按钮 -->
            <div id="game-controls">
                <button id="pause-btn" class="control-btn">暂停</button>
                <button id="reset-btn" class="control-btn">重新开始</button>
                <button id="menu-btn" class="control-btn">返回菜单</button>
            </div>
        </div>

        <!-- 设置面板 -->
        <div id="settings-panel" class="menu-panel hidden">
            <h2>游戏设置</h2>
            <div class="setting-item">
                <label>音效音量:</label>
                <input type="range" id="sound-volume" min="0" max="100" value="50">
            </div>
            <div class="setting-item">
                <label>图形质量:</label>
                <select id="graphics-quality">
                    <option value="low">低</option>
                    <option value="medium" selected>中</option>
                    <option value="high">高</option>
                </select>
            </div>
            <div class="setting-item">
                <label>显示轨迹预测:</label>
                <input type="checkbox" id="show-trajectory" checked>
            </div>
            <button id="settings-back-btn" class="menu-btn">返回</button>
        </div>

        <!-- 帮助面板 -->
        <div id="help-panel" class="menu-panel hidden">
            <h2>游戏帮助</h2>
            <div class="help-content">
                <h3>游戏规则:</h3>
                <ul>
                    <li>这是一个8球台球游戏</li>
                    <li>玩家需要先击入自己的目标球(实心球或花球)</li>
                    <li>最后击入8号黑球获胜</li>
                    <li>犯规会让对手获得自由球权</li>
                </ul>
                <h3>操作方法:</h3>
                <ul>
                    <li>鼠标移动: 瞄准方向</li>
                    <li>鼠标左键: 击球</li>
                    <li>滚轮: 调整视角</li>
                    <li>右键拖拽: 旋转视角</li>
                </ul>
            </div>
            <button id="help-back-btn" class="menu-btn">返回</button>
        </div>

        <!-- 游戏结束面板 -->
        <div id="game-over-panel" class="menu-panel hidden">
            <h2>游戏结束</h2>
            <div id="winner-text"></div>
            <div class="menu-buttons">
                <button id="play-again-btn" class="menu-btn">再玩一局</button>
                <button id="back-to-menu-btn" class="menu-btn">返回菜单</button>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loading" class="hidden">
        <div class="loading-spinner"></div>
        <div class="loading-text">游戏加载中...</div>
    </div>

    <!-- 使用CDN加载依赖 -->
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <script src="https://unpkg.com/cannon-es@0.20.0/dist/cannon-es.js"></script>
    <script>
        // 检查库是否加载成功
        function checkLibraries() {
            if (typeof THREE === 'undefined') {
                alert('Three.js 加载失败，请检查网络连接或刷新页面重试');
                return false;
            }
            if (typeof CANNON === 'undefined') {
                alert('Cannon.js 加载失败，请检查网络连接或刷新页面重试');
                return false;
            }
            return true;
        }

        // 等待库加载完成后再加载游戏
        window.addEventListener('load', function() {
            if (checkLibraries()) {
                // 动态加载游戏脚本
                const script = document.createElement('script');
                script.src = 'game.js';
                script.onerror = function() {
                    alert('游戏脚本加载失败');
                };
                document.head.appendChild(script);
            }
        });
    </script>
</body>
</html>
