<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D台球游戏 - 独立版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            overflow: hidden;
            color: white;
        }

        #game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #game-canvas {
            display: block;
            width: 100%;
            height: 100%;
        }

        #ui-overlay {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 20px;
            border-radius: 10px;
            color: white;
        }

        #main-menu {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            z-index: 200;
        }

        #main-menu h1 {
            font-size: 3em;
            margin-bottom: 30px;
            color: #ffd700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .menu-buttons {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .menu-btn {
            padding: 15px 30px;
            font-size: 1.2em;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            font-weight: bold;
        }

        .menu-btn:hover {
            background: linear-gradient(45deg, #45a049, #4CAF50);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        #instructions {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            z-index: 100;
        }

        .control-info {
            margin: 5px 0;
            font-size: 14px;
        }

        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-top: 5px solid #ffd700;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .loading-text {
            margin-top: 20px;
            font-size: 1.2em;
            color: #ffd700;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none !important;
        }

        #error-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(244, 67, 54, 0.9);
            color: white;
            padding: 20px 30px;
            border-radius: 10px;
            font-size: 1.2em;
            font-weight: bold;
            z-index: 2000;
            text-align: center;
            max-width: 80%;
        }
    </style>
</head>
<body>
    <div id="game-container">
        <canvas id="game-canvas"></canvas>
        
        <div id="main-menu">
            <h1>3D台球游戏</h1>
            <div class="menu-buttons">
                <button id="start-game-btn" class="menu-btn">开始游戏</button>
                <button id="help-btn" class="menu-btn">游戏帮助</button>
            </div>
        </div>

        <div id="ui-overlay" class="hidden">
            <h3>3D台球游戏</h3>
            <div>状态: <span id="game-status">游戏中</span></div>
            <div>当前玩家: <span id="current-player">玩家1</span></div>
            <div>力度: <span id="power-display">0%</span></div>
            <button id="reset-btn" class="menu-btn" style="margin-top: 10px;">重新开始</button>
            <button id="menu-btn" class="menu-btn" style="margin-top: 5px;">返回菜单</button>
        </div>

        <div id="instructions" class="hidden">
            <div class="control-info"><strong>操作说明:</strong></div>
            <div class="control-info">鼠标移动: 瞄准方向</div>
            <div class="control-info">鼠标左键按住: 蓄力击球</div>
            <div class="control-info">鼠标滚轮: 调整视角距离</div>
            <div class="control-info">空格键: 暂停游戏</div>
        </div>
    </div>

    <div id="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">游戏加载中...</div>
    </div>

    <div id="error-message" class="hidden">
        <div id="error-text"></div>
        <button onclick="location.reload()" style="margin-top: 15px; padding: 10px 20px; background: #fff; color: #333; border: none; border-radius: 5px; cursor: pointer;">刷新页面</button>
    </div>

    <!-- 使用多个CDN源确保加载成功 -->
    <script>
        // 错误处理函数
        function showError(message) {
            document.getElementById('error-text').textContent = message;
            document.getElementById('error-message').classList.remove('hidden');
            document.getElementById('loading').classList.add('hidden');
        }

        // 加载Three.js
        function loadThreeJS() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.onload = () => {
                    if (typeof THREE !== 'undefined') {
                        console.log('Three.js 加载成功');
                        resolve();
                    } else {
                        reject(new Error('Three.js 加载失败'));
                    }
                };
                script.onerror = () => reject(new Error('Three.js 脚本加载失败'));
                
                // 尝试多个CDN源
                const sources = [
                    'https://unpkg.com/three@0.158.0/build/three.min.js',
                    'https://cdnjs.cloudflare.com/ajax/libs/three.js/r158/three.min.js',
                    'https://cdn.jsdelivr.net/npm/three@0.158.0/build/three.min.js'
                ];
                
                script.src = sources[0];
                document.head.appendChild(script);
            });
        }

        // 加载Cannon.js
        function loadCannonJS() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.onload = () => {
                    if (typeof CANNON !== 'undefined') {
                        console.log('Cannon.js 加载成功');
                        resolve();
                    } else {
                        reject(new Error('Cannon.js 加载失败'));
                    }
                };
                script.onerror = () => reject(new Error('Cannon.js 脚本加载失败'));

                // 使用正确的cannon-es CDN链接
                script.src = 'https://cdn.jsdelivr.net/npm/cannon-es@0.20.0/dist/cannon-es.js';
                document.head.appendChild(script);
            });
        }

        // 初始化游戏
        async function initGame() {
            try {
                // 加载依赖库
                await loadThreeJS();
                await loadCannonJS();
                
                // 创建简化的台球游戏
                window.game = new SimpleBilliardGame();
                await window.game.init();
                
                document.getElementById('loading').classList.add('hidden');
                console.log('游戏初始化完成');
                
            } catch (error) {
                console.error('游戏初始化失败:', error);
                showError('游戏初始化失败: ' + error.message + '\n请检查网络连接并刷新页面重试');
            }
        }

        // 启动初始化
        window.addEventListener('load', initGame);
    </script>

    <script>
        // 简化的台球游戏类
        class SimpleBilliardGame {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.canvas = null;
                
                this.world = null;
                this.balls = [];
                this.table = null;
                
                this.mouse = new THREE.Vector2();
                this.raycaster = new THREE.Raycaster();
                this.isAiming = false;
                this.power = 0;
                this.currentPlayer = 1;
                this.gameStarted = false;
                
                this.clock = new THREE.Clock();
                this.animationId = null;
            }

            async init() {
                try {
                    this.canvas = document.getElementById('game-canvas');
                    if (!this.canvas) {
                        throw new Error('找不到游戏canvas元素');
                    }

                    this.initThreeJS();
                    this.initPhysics();
                    this.createTable();
                    this.createBalls();
                    this.setupEventListeners();
                    this.startGameLoop();
                    
                    console.log('游戏初始化完成');
                    
                } catch (error) {
                    console.error('游戏初始化失败:', error);
                    throw error;
                }
            }

            initThreeJS() {
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x1a1a1a);
                
                const aspect = window.innerWidth / window.innerHeight;
                this.camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);
                this.camera.position.set(0, 8, 12);
                this.camera.lookAt(0, 0, 0);
                
                this.renderer = new THREE.WebGLRenderer({ 
                    canvas: this.canvas,
                    antialias: true
                });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                
                // 添加光照
                const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
                this.scene.add(ambientLight);
                
                const mainLight = new THREE.DirectionalLight(0xffffff, 1);
                mainLight.position.set(10, 20, 10);
                mainLight.castShadow = true;
                mainLight.shadow.mapSize.width = 2048;
                mainLight.shadow.mapSize.height = 2048;
                this.scene.add(mainLight);
                
                const spotLight = new THREE.SpotLight(0xffffff, 0.8);
                spotLight.position.set(0, 15, 0);
                spotLight.target.position.set(0, 0, 0);
                spotLight.angle = Math.PI / 3;
                spotLight.castShadow = true;
                this.scene.add(spotLight);
                this.scene.add(spotLight.target);
            }

            initPhysics() {
                this.world = new CANNON.World();
                this.world.gravity.set(0, -9.82, 0);
                this.world.broadphase = new CANNON.NaiveBroadphase();
                this.world.solver.iterations = 10;
                this.world.allowSleep = true;
            }

            createTable() {
                // 台面
                const tableGeometry = new THREE.BoxGeometry(9, 0.1, 4.5);
                const tableMaterial = new THREE.MeshLambertMaterial({ color: 0x0d5016 });
                this.table = new THREE.Mesh(tableGeometry, tableMaterial);
                this.table.position.set(0, -0.05, 0);
                this.table.receiveShadow = true;
                this.scene.add(this.table);
                
                // 台面物理体
                const tableShape = new CANNON.Box(new CANNON.Vec3(4.5, 0.05, 2.25));
                const tableBody = new CANNON.Body({ mass: 0 });
                tableBody.addShape(tableShape);
                tableBody.position.set(0, -0.05, 0);
                this.world.add(tableBody);
                
                // 边框
                this.createCushions();
            }

            createCushions() {
                const cushionMaterial = new THREE.MeshLambertMaterial({ color: 0x2d5a2d });
                const cushionHeight = 0.1;
                const cushionThickness = 0.1;
                
                // 创建边框几何体
                const positions = [
                    { pos: [0, cushionHeight / 2, 2.25 + cushionThickness / 2], size: [4.5, cushionHeight / 2, cushionThickness / 2] },
                    { pos: [0, cushionHeight / 2, -2.25 - cushionThickness / 2], size: [4.5, cushionHeight / 2, cushionThickness / 2] },
                    { pos: [-4.5 - cushionThickness / 2, cushionHeight / 2, 0], size: [cushionThickness / 2, cushionHeight / 2, 2.25] },
                    { pos: [4.5 + cushionThickness / 2, cushionHeight / 2, 0], size: [cushionThickness / 2, cushionHeight / 2, 2.25] }
                ];
                
                positions.forEach(({ pos, size }) => {
                    // 视觉边框
                    const geometry = new THREE.BoxGeometry(size[0] * 2, size[1] * 2, size[2] * 2);
                    const mesh = new THREE.Mesh(geometry, cushionMaterial);
                    mesh.position.set(...pos);
                    mesh.castShadow = true;
                    this.scene.add(mesh);
                    
                    // 物理边框
                    const shape = new CANNON.Box(new CANNON.Vec3(...size));
                    const body = new CANNON.Body({ mass: 0 });
                    body.addShape(shape);
                    body.position.set(...pos);
                    this.world.add(body);
                });
            }

            createBalls() {
                const ballRadius = 0.057;
                const ballMass = 0.16;
                
                // 球的颜色
                const colors = [
                    0xffffff, // 白球
                    0xffff00, 0x0000ff, 0xff0000, 0x800080, 0xffa500, 0x008000, 0x800000, // 1-7号球
                    0x000000, // 8号球
                    0xffff00, 0x0000ff, 0xff0000, 0x800080, 0xffa500, 0x008000, 0x800000  // 9-15号球
                ];
                
                // 创建白球
                this.createBall(0, -6, 0, 0, colors[0]);
                
                // 创建彩球（三角排列）
                const startX = 6;
                const spacing = ballRadius * 2.1;
                let ballIndex = 1;
                
                // 5排球的三角排列
                for (let row = 0; row < 5; row++) {
                    for (let col = 0; col <= row; col++) {
                        const x = startX + row * spacing;
                        const z = (col - row / 2) * spacing;
                        this.createBall(ballIndex, x, 0, z, colors[ballIndex]);
                        ballIndex++;
                    }
                }
            }

            createBall(number, x, y, z, color) {
                const ballRadius = 0.057;
                const ballMass = 0.16;
                
                // 创建球的几何体和材质
                const ballGeometry = new THREE.SphereGeometry(ballRadius, 32, 16);
                const ballMaterial = new THREE.MeshPhongMaterial({ 
                    color: color,
                    shininess: 100,
                    specular: 0x222222
                });
                
                const ballMesh = new THREE.Mesh(ballGeometry, ballMaterial);
                ballMesh.position.set(x, y + ballRadius, z);
                ballMesh.castShadow = true;
                ballMesh.receiveShadow = true;
                ballMesh.userData = { number: number, type: 'ball' };
                this.scene.add(ballMesh);
                
                // 创建物理体
                const ballShape = new CANNON.Sphere(ballRadius);
                const ballBody = new CANNON.Body({ 
                    mass: ballMass
                });
                ballBody.addShape(ballShape);
                ballBody.position.set(x, y + ballRadius, z);
                ballBody.linearDamping = 0.1;
                ballBody.angularDamping = 0.1;
                this.world.add(ballBody);
                
                this.balls.push({
                    number: number,
                    mesh: ballMesh,
                    body: ballBody
                });
            }

            setupEventListeners() {
                // 菜单按钮
                document.getElementById('start-game-btn').addEventListener('click', () => {
                    this.startGame();
                });
                
                document.getElementById('help-btn').addEventListener('click', () => {
                    alert('游戏操作说明：\n\n鼠标移动: 瞄准方向\n鼠标左键按住: 蓄力击球\n鼠标滚轮: 调整视角距离\n空格键: 暂停游戏\n\n目标：击入所有球！');
                });
                
                document.getElementById('reset-btn').addEventListener('click', () => {
                    this.resetGame();
                });
                
                document.getElementById('menu-btn').addEventListener('click', () => {
                    this.showMainMenu();
                });
                
                // 游戏控制
                this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
                this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
                this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
                this.canvas.addEventListener('wheel', this.onMouseWheel.bind(this));
                
                window.addEventListener('keydown', this.onKeyDown.bind(this));
                window.addEventListener('resize', this.onWindowResize.bind(this));
            }

            startGame() {
                this.gameStarted = true;
                document.getElementById('main-menu').classList.add('hidden');
                document.getElementById('ui-overlay').classList.remove('hidden');
                document.getElementById('instructions').classList.remove('hidden');
            }

            showMainMenu() {
                this.gameStarted = false;
                document.getElementById('main-menu').classList.remove('hidden');
                document.getElementById('ui-overlay').classList.add('hidden');
                document.getElementById('instructions').classList.add('hidden');
            }

            resetGame() {
                // 重置球的位置
                this.balls[0].body.position.set(-6, 0.057, 0);
                this.balls[0].body.velocity.set(0, 0, 0);
                this.balls[0].body.angularVelocity.set(0, 0, 0);
                
                const startX = 6;
                const spacing = 0.057 * 2.1;
                let ballIndex = 1;
                
                for (let row = 0; row < 5; row++) {
                    for (let col = 0; col <= row; col++) {
                        const x = startX + row * spacing;
                        const z = (col - row / 2) * spacing;
                        this.balls[ballIndex].body.position.set(x, 0.057, z);
                        this.balls[ballIndex].body.velocity.set(0, 0, 0);
                        this.balls[ballIndex].body.angularVelocity.set(0, 0, 0);
                        ballIndex++;
                    }
                }
                
                this.power = 0;
                this.updatePowerDisplay(0);
            }

            onMouseMove(event) {
                if (!this.gameStarted) return;
                
                const rect = this.canvas.getBoundingClientRect();
                this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
                this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
            }

            onMouseDown(event) {
                if (!this.gameStarted || event.button !== 0) return;
                this.startAiming();
            }

            onMouseUp(event) {
                if (!this.gameStarted || event.button !== 0 || !this.isAiming) return;
                this.shoot();
            }

            onMouseWheel(event) {
                if (!this.gameStarted) return;
                
                const delta = event.deltaY * 0.01;
                const direction = new THREE.Vector3();
                this.camera.getWorldDirection(direction);
                this.camera.position.addScaledVector(direction, delta);
            }

            onKeyDown(event) {
                if (event.code === 'Space') {
                    event.preventDefault();
                    // 简单的暂停功能
                }
            }

            onWindowResize() {
                this.camera.aspect = window.innerWidth / window.innerHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(window.innerWidth, window.innerHeight);
            }

            startAiming() {
                if (!this.canShoot()) return;
                
                this.isAiming = true;
                this.power = 0;
                this.startPowerAccumulation();
            }

            startPowerAccumulation() {
                const startTime = Date.now();
                const maxPower = 100;
                const accumTime = 2000; // 2秒达到最大力度
                
                const updatePower = () => {
                    if (!this.isAiming) return;
                    
                    const elapsed = Date.now() - startTime;
                    this.power = Math.min((elapsed / accumTime) * maxPower, maxPower);
                    
                    this.updatePowerDisplay(this.power);
                    
                    if (this.isAiming) {
                        requestAnimationFrame(updatePower);
                    }
                };
                
                updatePower();
            }

            shoot() {
                if (!this.isAiming || this.power === 0) return;
                
                this.isAiming = false;
                
                // 计算击球方向
                this.raycaster.setFromCamera(this.mouse, this.camera);
                const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0);
                const intersectPoint = new THREE.Vector3();
                this.raycaster.ray.intersectPlane(plane, intersectPoint);
                
                // 获取白球
                const cueBall = this.balls[0];
                const cueBallPos = cueBall.body.position;
                
                // 计算方向和力度
                const direction = new THREE.Vector3()
                    .subVectors(intersectPoint, new THREE.Vector3(cueBallPos.x, cueBallPos.y, cueBallPos.z))
                    .normalize();
                
                const force = (this.power / 100) * 15;
                
                // 应用力到白球
                const impulse = new CANNON.Vec3(
                    direction.x * force,
                    0,
                    direction.z * force
                );
                cueBall.body.applyImpulse(impulse);
                
                this.power = 0;
                this.updatePowerDisplay(0);
            }

            canShoot() {
                return this.balls.every(ball => 
                    ball.body.velocity.length() < 0.1 && 
                    ball.body.angularVelocity.length() < 0.1
                );
            }

            updatePowerDisplay(power) {
                const powerElement = document.getElementById('power-display');
                if (powerElement) {
                    powerElement.textContent = `${Math.round(power)}%`;
                }
            }

            startGameLoop() {
                const animate = () => {
                    this.animationId = requestAnimationFrame(animate);
                    
                    const deltaTime = this.clock.getDelta();
                    
                    // 更新物理
                    this.world.step(1/60, deltaTime, 3);
                    
                    // 同步球的位置
                    this.balls.forEach(ball => {
                        ball.mesh.position.copy(ball.body.position);
                        ball.mesh.quaternion.copy(ball.body.quaternion);
                    });
                    
                    // 渲染场景
                    this.renderer.render(this.scene, this.camera);
                };
                
                animate();
            }
        }
    </script>
</body>
</html>
