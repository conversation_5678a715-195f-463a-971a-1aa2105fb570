// 事件发射器类
class EventEmitter {
    constructor() {
        this.events = {};
    }

    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }

    off(event, callback) {
        if (!this.events[event]) return;
        
        const index = this.events[event].indexOf(callback);
        if (index > -1) {
            this.events[event].splice(index, 1);
        }
    }

    emit(event, ...args) {
        if (!this.events[event]) return;
        
        this.events[event].forEach(callback => {
            try {
                callback(...args);
            } catch (error) {
                console.error(`事件处理器错误 (${event}):`, error);
            }
        });
    }

    once(event, callback) {
        const onceCallback = (...args) => {
            callback(...args);
            this.off(event, onceCallback);
        };
        this.on(event, onceCallback);
    }

    removeAllListeners(event) {
        if (event) {
            delete this.events[event];
        } else {
            this.events = {};
        }
    }
}

// 物理引擎类
class Physics {
    constructor() {
        this.world = null;
        this.bodies = new Map();
        this.isInitialized = false;
        this.gravity = -9.82;
        this.timeStep = 1 / 60;
        this.maxSubSteps = 3;
        this.materials = {};
    }

    async init() {
        try {
            this.world = new CANNON.World({
                gravity: new CANNON.Vec3(0, this.gravity, 0),
                broadphase: new CANNON.NaiveBroadphase(),
                solver: new CANNON.GSSolver()
            });

            this.world.solver.iterations = 10;
            this.world.solver.tolerance = 0.1;
            this.world.allowSleep = true;

            this.createMaterials();
            this.createContactMaterials();
            
            this.isInitialized = true;
            console.log('物理引擎初始化完成');
            
        } catch (error) {
            console.error('物理引擎初始化失败:', error);
            throw error;
        }
    }

    createMaterials() {
        this.materials.ball = new CANNON.Material('ball');
        this.materials.ball.friction = 0.1;
        this.materials.ball.restitution = 0.9;

        this.materials.table = new CANNON.Material('table');
        this.materials.table.friction = 0.8;
        this.materials.table.restitution = 0.1;

        this.materials.cushion = new CANNON.Material('cushion');
        this.materials.cushion.friction = 0.2;
        this.materials.cushion.restitution = 0.8;
    }

    createContactMaterials() {
        const ballBallContact = new CANNON.ContactMaterial(
            this.materials.ball,
            this.materials.ball,
            {
                friction: 0.05,
                restitution: 0.95,
                contactEquationStiffness: 1e8,
                contactEquationRelaxation: 3
            }
        );
        this.world.addContactMaterial(ballBallContact);

        const ballTableContact = new CANNON.ContactMaterial(
            this.materials.ball,
            this.materials.table,
            {
                friction: 0.4,
                restitution: 0.1,
                contactEquationStiffness: 1e8,
                contactEquationRelaxation: 3
            }
        );
        this.world.addContactMaterial(ballTableContact);

        const ballCushionContact = new CANNON.ContactMaterial(
            this.materials.ball,
            this.materials.cushion,
            {
                friction: 0.2,
                restitution: 0.8,
                contactEquationStiffness: 1e8,
                contactEquationRelaxation: 3
            }
        );
        this.world.addContactMaterial(ballCushionContact);
    }

    createBallBody(radius = 0.057, mass = 0.16) {
        const shape = new CANNON.Sphere(radius);
        const body = new CANNON.Body({
            mass: mass,
            material: this.materials.ball,
            shape: shape,
            linearDamping: 0.1,
            angularDamping: 0.1
        });

        body.sleepSpeedLimit = 0.1;
        body.sleepTimeLimit = 1;

        this.world.addBody(body);
        return body;
    }

    createTableBody(width, height, length) {
        const tableShape = new CANNON.Box(new CANNON.Vec3(width / 2, height / 2, length / 2));
        const tableBody = new CANNON.Body({
            mass: 0,
            material: this.materials.table,
            shape: tableShape,
            position: new CANNON.Vec3(0, -height / 2, 0)
        });
        this.world.addBody(tableBody);
        return [tableBody];
    }

    createCushionBodies(tableWidth, tableLength, cushionHeight = 0.1, cushionThickness = 0.1) {
        const bodies = [];
        const halfWidth = tableWidth / 2;
        const halfLength = tableLength / 2;

        const longCushionShape = new CANNON.Box(new CANNON.Vec3(halfLength, cushionHeight / 2, cushionThickness / 2));
        
        const topCushion = new CANNON.Body({
            mass: 0,
            material: this.materials.cushion,
            shape: longCushionShape,
            position: new CANNON.Vec3(0, cushionHeight / 2, halfWidth + cushionThickness / 2)
        });
        this.world.addBody(topCushion);
        bodies.push(topCushion);

        const bottomCushion = new CANNON.Body({
            mass: 0,
            material: this.materials.cushion,
            shape: longCushionShape,
            position: new CANNON.Vec3(0, cushionHeight / 2, -halfWidth - cushionThickness / 2)
        });
        this.world.addBody(bottomCushion);
        bodies.push(bottomCushion);

        const shortCushionShape = new CANNON.Box(new CANNON.Vec3(cushionThickness / 2, cushionHeight / 2, halfWidth));
        
        const leftCushion = new CANNON.Body({
            mass: 0,
            material: this.materials.cushion,
            shape: shortCushionShape,
            position: new CANNON.Vec3(-halfLength - cushionThickness / 2, cushionHeight / 2, 0)
        });
        this.world.addBody(leftCushion);
        bodies.push(leftCushion);

        const rightCushion = new CANNON.Body({
            mass: 0,
            material: this.materials.cushion,
            shape: shortCushionShape,
            position: new CANNON.Vec3(halfLength + cushionThickness / 2, cushionHeight / 2, 0)
        });
        this.world.addBody(rightCushion);
        bodies.push(rightCushion);

        return bodies;
    }

    applyForce(body, force, worldPoint) {
        if (!body || !force) return;
        
        const cannonForce = new CANNON.Vec3(force.x, force.y, force.z);
        const cannonPoint = worldPoint ? 
            new CANNON.Vec3(worldPoint.x, worldPoint.y, worldPoint.z) : 
            body.position;
            
        body.applyForce(cannonForce, cannonPoint);
    }

    applyImpulse(body, impulse, worldPoint) {
        if (!body || !impulse) return;
        
        const cannonImpulse = new CANNON.Vec3(impulse.x, impulse.y, impulse.z);
        const cannonPoint = worldPoint ? 
            new CANNON.Vec3(worldPoint.x, worldPoint.y, worldPoint.z) : 
            body.position;
            
        body.applyImpulse(cannonImpulse, cannonPoint);
    }

    setBodyPosition(body, position) {
        if (!body || !position) return;
        
        body.position.set(position.x, position.y, position.z);
        body.velocity.set(0, 0, 0);
        body.angularVelocity.set(0, 0, 0);
        body.wakeUp();
    }

    getBodyPosition(body) {
        if (!body) return null;
        
        return {
            x: body.position.x,
            y: body.position.y,
            z: body.position.z
        };
    }

    getBodyRotation(body) {
        if (!body) return null;
        
        return {
            x: body.quaternion.x,
            y: body.quaternion.y,
            z: body.quaternion.z,
            w: body.quaternion.w
        };
    }

    isBodySleeping(body) {
        if (!body) return true;
        
        return body.sleepState === CANNON.Body.SLEEPING || 
               (body.velocity.length() < 0.01 && body.angularVelocity.length() < 0.01);
    }

    wakeUpBody(body) {
        if (body) {
            body.wakeUp();
        }
    }

    sleepBody(body) {
        if (body) {
            body.sleep();
        }
    }

    removeBody(body) {
        if (body && this.world) {
            this.world.removeBody(body);
        }
    }

    update(deltaTime) {
        if (!this.world || !this.isInitialized) return;
        
        const clampedDeltaTime = Math.min(deltaTime, 0.1);
        
        try {
            this.world.step(this.timeStep, clampedDeltaTime, this.maxSubSteps);
        } catch (error) {
            console.error('物理更新错误:', error);
        }
    }

    destroy() {
        if (this.world) {
            while (this.world.bodies.length > 0) {
                this.world.removeBody(this.world.bodies[0]);
            }

            this.world.contactMaterials = [];
            this.world = null;
        }

        this.bodies.clear();
        this.materials = {};
        this.isInitialized = false;
    }
}

// 台球桌类
class Table {
    constructor() {
        this.mesh = null;
        this.cushions = [];
        this.pockets = [];
        this.physics = null;
        this.scene = null;

        this.width = 4.5;
        this.length = 9;
        this.height = 0.1;
        this.cushionHeight = 0.1;
        this.cushionThickness = 0.1;
        this.pocketRadius = 0.12;
        this.pocketDepth = 0.2;

        this.isInitialized = false;
    }

    async init(scene, physics) {
        try {
            this.scene = scene;
            this.physics = physics;

            await this.createTable();
            await this.createCushions();
            await this.createPockets();
            this.createPhysicsBodies();

            this.isInitialized = true;
            console.log('台球桌创建完成');

        } catch (error) {
            console.error('台球桌创建失败:', error);
            throw error;
        }
    }

    async createTable() {
        const tableGeometry = new THREE.BoxGeometry(this.length, this.height, this.width);
        const tableMaterial = new THREE.MeshLambertMaterial({
            color: 0x0d5016,
            roughness: 0.8,
            metalness: 0.1
        });

        this.mesh = new THREE.Mesh(tableGeometry, tableMaterial);
        this.mesh.position.set(0, -this.height / 2, 0);
        this.mesh.receiveShadow = true;
        this.mesh.castShadow = false;

        this.scene.add(this.mesh);

        await this.createTableFrame();
        await this.createTableMarkings();
    }

    async createTableFrame() {
        const frameHeight = 0.3;
        const frameThickness = 0.2;

        const frameGeometry = new THREE.BoxGeometry(
            this.length + frameThickness * 2,
            frameHeight,
            this.width + frameThickness * 2
        );

        const frameMaterial = new THREE.MeshLambertMaterial({
            color: 0x8B4513,
            roughness: 0.9,
            metalness: 0.1
        });

        const frame = new THREE.Mesh(frameGeometry, frameMaterial);
        frame.position.set(0, -frameHeight / 2 - this.height, 0);
        frame.receiveShadow = true;
        frame.castShadow = true;

        this.scene.add(frame);
    }

    async createTableMarkings() {
        const centerLineGeometry = new THREE.PlaneGeometry(0.02, this.width);
        const centerLineMaterial = new THREE.MeshBasicMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.8
        });

        const centerLine = new THREE.Mesh(centerLineGeometry, centerLineMaterial);
        centerLine.rotation.x = -Math.PI / 2;
        centerLine.position.set(0, 0.001, 0);
        this.scene.add(centerLine);

        const breakLineGeometry = new THREE.PlaneGeometry(0.02, this.width);
        const breakLine = new THREE.Mesh(breakLineGeometry, centerLineMaterial);
        breakLine.rotation.x = -Math.PI / 2;
        breakLine.position.set(-this.length / 4, 0.001, 0);
        this.scene.add(breakLine);

        const centerDotGeometry = new THREE.CircleGeometry(0.02, 16);
        const centerDot = new THREE.Mesh(centerDotGeometry, centerLineMaterial);
        centerDot.rotation.x = -Math.PI / 2;
        centerDot.position.set(0, 0.002, 0);
        this.scene.add(centerDot);

        const breakDot = new THREE.Mesh(centerDotGeometry, centerLineMaterial);
        breakDot.rotation.x = -Math.PI / 2;
        breakDot.position.set(-this.length / 4, 0.002, 0);
        this.scene.add(breakDot);
    }

    async createCushions() {
        const cushionMaterial = new THREE.MeshLambertMaterial({
            color: 0x2d5a2d,
            roughness: 0.7,
            metalness: 0.2
        });

        const longCushionGeometry = new THREE.BoxGeometry(
            this.length,
            this.cushionHeight,
            this.cushionThickness
        );

        const topCushion = new THREE.Mesh(longCushionGeometry, cushionMaterial);
        topCushion.position.set(0, this.cushionHeight / 2, this.width / 2 + this.cushionThickness / 2);
        topCushion.castShadow = true;
        topCushion.receiveShadow = true;
        this.scene.add(topCushion);
        this.cushions.push(topCushion);

        const bottomCushion = new THREE.Mesh(longCushionGeometry, cushionMaterial);
        bottomCushion.position.set(0, this.cushionHeight / 2, -this.width / 2 - this.cushionThickness / 2);
        bottomCushion.castShadow = true;
        bottomCushion.receiveShadow = true;
        this.scene.add(bottomCushion);
        this.cushions.push(bottomCushion);

        const shortCushionGeometry = new THREE.BoxGeometry(
            this.cushionThickness,
            this.cushionHeight,
            this.width
        );

        const leftCushion = new THREE.Mesh(shortCushionGeometry, cushionMaterial);
        leftCushion.position.set(-this.length / 2 - this.cushionThickness / 2, this.cushionHeight / 2, 0);
        leftCushion.castShadow = true;
        leftCushion.receiveShadow = true;
        this.scene.add(leftCushion);
        this.cushions.push(leftCushion);

        const rightCushion = new THREE.Mesh(shortCushionGeometry, cushionMaterial);
        rightCushion.position.set(this.length / 2 + this.cushionThickness / 2, this.cushionHeight / 2, 0);
        rightCushion.castShadow = true;
        rightCushion.receiveShadow = true;
        this.scene.add(rightCushion);
        this.cushions.push(rightCushion);
    }

    async createPockets() {
        const pocketPositions = [
            { x: -this.length / 2, z: -this.width / 2, name: 'corner1' },
            { x: -this.length / 2, z: this.width / 2, name: 'corner2' },
            { x: this.length / 2, z: -this.width / 2, name: 'corner3' },
            { x: this.length / 2, z: this.width / 2, name: 'corner4' },
            { x: 0, z: -this.width / 2, name: 'side1' },
            { x: 0, z: this.width / 2, name: 'side2' }
        ];

        const pocketMaterial = new THREE.MeshLambertMaterial({
            color: 0x000000,
            roughness: 0.9,
            metalness: 0.1
        });

        pocketPositions.forEach(pos => {
            const pocketGeometry = new THREE.CylinderGeometry(
                this.pocketRadius,
                this.pocketRadius * 0.8,
                this.pocketDepth,
                16
            );

            const pocket = new THREE.Mesh(pocketGeometry, pocketMaterial);
            pocket.position.set(pos.x, -this.pocketDepth / 2, pos.z);
            pocket.receiveShadow = true;
            pocket.userData = { name: pos.name, isPocket: true };

            this.scene.add(pocket);
            this.pockets.push(pocket);

            this.createPocketRim(pos.x, pos.z);
        });
    }

    createPocketRim(x, z) {
        const rimGeometry = new THREE.TorusGeometry(
            this.pocketRadius + 0.01,
            0.005,
            8,
            16
        );

        const rimMaterial = new THREE.MeshLambertMaterial({
            color: 0xC0C0C0,
            metalness: 0.8,
            roughness: 0.2
        });

        const rim = new THREE.Mesh(rimGeometry, rimMaterial);
        rim.position.set(x, 0.002, z);
        rim.rotation.x = Math.PI / 2;
        rim.castShadow = true;

        this.scene.add(rim);
    }

    createPhysicsBodies() {
        if (!this.physics) return;

        this.physics.createTableBody(this.width, this.height, this.length);
        this.physics.createCushionBodies(this.width, this.length, this.cushionHeight, this.cushionThickness);
    }

    checkBallInPocket(ballPosition, ballRadius = 0.057) {
        for (let i = 0; i < this.pockets.length; i++) {
            const pocket = this.pockets[i];
            const pocketPos = pocket.position;

            const distance = Math.sqrt(
                Math.pow(ballPosition.x - pocketPos.x, 2) +
                Math.pow(ballPosition.z - pocketPos.z, 2)
            );

            if (distance < this.pocketRadius - ballRadius && ballPosition.y < 0) {
                return {
                    inPocket: true,
                    pocketIndex: i,
                    pocketName: pocket.userData.name
                };
            }
        }

        return { inPocket: false };
    }

    getBounds() {
        return {
            minX: -this.length / 2,
            maxX: this.length / 2,
            minZ: -this.width / 2,
            maxZ: this.width / 2,
            minY: 0,
            maxY: this.cushionHeight
        };
    }

    destroy() {
        if (this.mesh && this.scene) {
            this.scene.remove(this.mesh);
            this.mesh.geometry.dispose();
            this.mesh.material.dispose();
        }

        this.cushions.forEach(cushion => {
            if (this.scene) {
                this.scene.remove(cushion);
                cushion.geometry.dispose();
                cushion.material.dispose();
            }
        });

        this.pockets.forEach(pocket => {
            if (this.scene) {
                this.scene.remove(pocket);
                pocket.geometry.dispose();
                pocket.material.dispose();
            }
        });

        this.mesh = null;
        this.cushions = [];
        this.pockets = [];
        this.physics = null;
        this.scene = null;
        this.isInitialized = false;
    }
}

// 台球类
class Ball {
    constructor(type, number) {
        this.type = type;
        this.number = number;

        this.mesh = null;
        this.body = null;
        this.scene = null;
        this.physics = null;

        this.radius = 0.057;
        this.mass = 0.16;

        this.isInPocket = false;
        this.isVisible = true;
        this.lastPosition = new THREE.Vector3();
        this.velocity = new THREE.Vector3();

        this.colors = this.getBallColors();
        this.isInitialized = false;
    }

    getBallColors() {
        const colors = {
            0: 0xffffff,  // 白球
            1: 0xffff00,  // 黄色
            2: 0x0000ff,  // 蓝色
            3: 0xff0000,  // 红色
            4: 0x800080,  // 紫色
            5: 0xffa500,  // 橙色
            6: 0x008000,  // 绿色
            7: 0x800000,  // 栗色
            8: 0x000000,  // 黑色
            9: 0xffff00,  // 黄色条纹
            10: 0x0000ff, // 蓝色条纹
            11: 0xff0000, // 红色条纹
            12: 0x800080, // 紫色条纹
            13: 0xffa500, // 橙色条纹
            14: 0x008000, // 绿色条纹
            15: 0x800000  // 栗色条纹
        };

        return colors;
    }

    async init(scene, physics) {
        try {
            this.scene = scene;
            this.physics = physics;

            await this.createGeometry();
            await this.createMaterial();
            this.createMesh();
            this.createPhysicsBody();

            this.scene.add(this.mesh);

            this.isInitialized = true;
            console.log(`${this.number}号球创建完成`);

        } catch (error) {
            console.error(`${this.number}号球创建失败:`, error);
            throw error;
        }
    }

    async createGeometry() {
        this.geometry = new THREE.SphereGeometry(this.radius, 32, 16);
    }

    async createMaterial() {
        const baseColor = this.colors[this.number] || 0xffffff;

        if (this.type === 'cue') {
            this.material = new THREE.MeshPhongMaterial({
                color: baseColor,
                shininess: 100,
                specular: 0x222222,
                transparent: false
            });
        } else if (this.type === 'eight') {
            this.material = new THREE.MeshPhongMaterial({
                color: baseColor,
                shininess: 100,
                specular: 0x222222
            });
        } else if (this.type === 'solid') {
            this.material = new THREE.MeshPhongMaterial({
                color: baseColor,
                shininess: 80,
                specular: 0x111111
            });
        } else if (this.type === 'stripe') {
            this.material = await this.createStripeMaterial(baseColor);
        }

        if (this.number > 0) {
            await this.addNumberTexture();
        }
    }

    async createStripeMaterial(baseColor) {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const context = canvas.getContext('2d');

        context.fillStyle = '#ffffff';
        context.fillRect(0, 0, 256, 256);

        context.fillStyle = `#${baseColor.toString(16).padStart(6, '0')}`;
        const stripeWidth = 32;
        for (let i = 0; i < 256; i += stripeWidth * 2) {
            context.fillRect(i, 0, stripeWidth, 256);
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(4, 2);

        return new THREE.MeshPhongMaterial({
            map: texture,
            shininess: 80,
            specular: 0x111111
        });
    }

    async addNumberTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 128;
        canvas.height = 128;
        const context = canvas.getContext('2d');

        context.fillStyle = '#ffffff';
        context.beginPath();
        context.arc(64, 64, 50, 0, Math.PI * 2);
        context.fill();

        context.fillStyle = '#000000';
        context.font = 'bold 48px Arial';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText(this.number.toString(), 64, 64);

        const numberTexture = new THREE.CanvasTexture(canvas);

        if (this.material) {
            const numberGeometry = new THREE.CircleGeometry(this.radius * 0.3, 16);
            const numberMaterial = new THREE.MeshBasicMaterial({
                map: numberTexture,
                transparent: true,
                alphaTest: 0.1
            });

            this.numberDecal = new THREE.Mesh(numberGeometry, numberMaterial);
            this.numberDecal.position.set(0, 0, this.radius + 0.001);
        }
    }

    createMesh() {
        this.mesh = new THREE.Mesh(this.geometry, this.material);
        this.mesh.castShadow = true;
        this.mesh.receiveShadow = true;
        this.mesh.userData = {
            type: 'ball',
            ballType: this.type,
            number: this.number,
            ballInstance: this
        };

        if (this.numberDecal) {
            this.mesh.add(this.numberDecal);
        }
    }

    createPhysicsBody() {
        if (!this.physics) return;

        this.body = this.physics.createBallBody(this.radius, this.mass);
        this.body.userData = {
            type: 'ball',
            ballInstance: this
        };
    }

    setPosition(x, y, z) {
        const position = new THREE.Vector3(x, y, z);

        if (this.mesh) {
            this.mesh.position.copy(position);
        }

        if (this.body && this.physics) {
            this.physics.setBodyPosition(this.body, position);
        }

        this.lastPosition.copy(position);
    }

    getPosition() {
        if (this.body && this.physics) {
            const pos = this.physics.getBodyPosition(this.body);
            return new THREE.Vector3(pos.x, pos.y, pos.z);
        } else if (this.mesh) {
            return this.mesh.position.clone();
        }
        return new THREE.Vector3();
    }

    applyForce(direction, magnitude) {
        if (!this.body || !this.physics) return;

        const force = direction.clone().multiplyScalar(magnitude);
        this.physics.applyImpulse(this.body, force);
        this.physics.wakeUpBody(this.body);
    }

    stop() {
        if (this.body) {
            this.body.velocity.set(0, 0, 0);
            this.body.angularVelocity.set(0, 0, 0);
            this.physics.sleepBody(this.body);
        }
    }

    isStopped() {
        if (this.body && this.physics) {
            return this.physics.isBodySleeping(this.body);
        }
        return true;
    }

    getVelocity() {
        if (this.body) {
            return new THREE.Vector3(
                this.body.velocity.x,
                this.body.velocity.y,
                this.body.velocity.z
            );
        }
        return new THREE.Vector3();
    }

    setVisible(visible) {
        this.isVisible = visible;
        if (this.mesh) {
            this.mesh.visible = visible;
        }
    }

    enterPocket() {
        this.isInPocket = true;
        this.setVisible(false);

        if (this.body && this.physics) {
            this.physics.removeBody(this.body);
            this.body = null;
        }
    }

    exitPocket() {
        this.isInPocket = false;
        this.setVisible(true);
        this.createPhysicsBody();
    }

    reset() {
        this.isInPocket = false;
        this.setVisible(true);
        this.stop();

        if (!this.body) {
            this.createPhysicsBody();
        }
    }

    update() {
        if (!this.isInitialized || this.isInPocket) return;

        if (this.body && this.mesh && this.physics) {
            const position = this.physics.getBodyPosition(this.body);
            const rotation = this.physics.getBodyRotation(this.body);

            this.mesh.position.set(position.x, position.y, position.z);
            this.mesh.quaternion.set(rotation.x, rotation.y, rotation.z, rotation.w);

            this.velocity = this.getVelocity();
        }
    }

    destroy() {
        if (this.mesh && this.scene) {
            this.scene.remove(this.mesh);
        }

        if (this.geometry) {
            this.geometry.dispose();
        }

        if (this.material) {
            if (this.material.map) {
                this.material.map.dispose();
            }
            this.material.dispose();
        }

        if (this.numberDecal) {
            this.numberDecal.geometry.dispose();
            this.numberDecal.material.dispose();
        }

        if (this.body && this.physics) {
            this.physics.removeBody(this.body);
        }

        this.mesh = null;
        this.body = null;
        this.geometry = null;
        this.material = null;
        this.numberDecal = null;
        this.scene = null;
        this.physics = null;
        this.isInitialized = false;
    }
}

// UI类
class UI extends EventEmitter {
    constructor() {
        super();

        this.elements = {};
        this.currentPanel = null;
        this.settings = {
            soundVolume: 50,
            graphicsQuality: 'medium',
            showTrajectory: true
        };

        this.isInitialized = false;
    }

    async init() {
        try {
            this.getElements();
            this.setupEventListeners();
            this.initializeSettings();

            this.isInitialized = true;
            console.log('UI初始化完成');

        } catch (error) {
            console.error('UI初始化失败:', error);
            throw error;
        }
    }

    getElements() {
        this.elements.mainMenu = document.getElementById('main-menu');
        this.elements.gameHUD = document.getElementById('game-hud');
        this.elements.settingsPanel = document.getElementById('settings-panel');
        this.elements.helpPanel = document.getElementById('help-panel');
        this.elements.gameOverPanel = document.getElementById('game-over-panel');

        this.elements.singlePlayerBtn = document.getElementById('single-player-btn');
        this.elements.twoPlayerBtn = document.getElementById('two-player-btn');
        this.elements.settingsBtn = document.getElementById('settings-btn');
        this.elements.helpBtn = document.getElementById('help-btn');
        this.elements.pauseBtn = document.getElementById('pause-btn');
        this.elements.resetBtn = document.getElementById('reset-btn');
        this.elements.menuBtn = document.getElementById('menu-btn');
        this.elements.settingsBackBtn = document.getElementById('settings-back-btn');
        this.elements.helpBackBtn = document.getElementById('help-back-btn');
        this.elements.playAgainBtn = document.getElementById('play-again-btn');
        this.elements.backToMenuBtn = document.getElementById('back-to-menu-btn');

        this.elements.player1Score = document.getElementById('player1-score');
        this.elements.player2Score = document.getElementById('player2-score');
        this.elements.currentPlayerName = document.getElementById('current-player-name');
        this.elements.powerFill = document.getElementById('power-fill');
        this.elements.powerValue = document.getElementById('power-value');
        this.elements.winnerText = document.getElementById('winner-text');

        this.elements.soundVolume = document.getElementById('sound-volume');
        this.elements.graphicsQuality = document.getElementById('graphics-quality');
        this.elements.showTrajectory = document.getElementById('show-trajectory');
    }

    setupEventListeners() {
        this.elements.singlePlayerBtn?.addEventListener('click', () => {
            this.emit('singlePlayer');
        });

        this.elements.twoPlayerBtn?.addEventListener('click', () => {
            this.emit('twoPlayer');
        });

        this.elements.settingsBtn?.addEventListener('click', () => {
            this.emit('settings');
        });

        this.elements.helpBtn?.addEventListener('click', () => {
            this.emit('help');
        });

        this.elements.pauseBtn?.addEventListener('click', () => {
            this.emit('pause');
        });

        this.elements.resetBtn?.addEventListener('click', () => {
            if (confirm('确定要重新开始游戏吗？')) {
                this.emit('reset');
            }
        });

        this.elements.menuBtn?.addEventListener('click', () => {
            if (confirm('确定要返回主菜单吗？当前游戏进度将丢失。')) {
                this.emit('backToMenu');
            }
        });

        this.elements.settingsBackBtn?.addEventListener('click', () => {
            this.showMainMenu();
        });

        this.elements.helpBackBtn?.addEventListener('click', () => {
            this.showMainMenu();
        });

        this.elements.playAgainBtn?.addEventListener('click', () => {
            this.emit('playAgain');
        });

        this.elements.backToMenuBtn?.addEventListener('click', () => {
            this.emit('backToMenu');
        });

        this.elements.soundVolume?.addEventListener('input', (e) => {
            this.settings.soundVolume = parseInt(e.target.value);
            this.emit('settingsChanged', this.settings);
        });

        this.elements.graphicsQuality?.addEventListener('change', (e) => {
            this.settings.graphicsQuality = e.target.value;
            this.emit('settingsChanged', this.settings);
        });

        this.elements.showTrajectory?.addEventListener('change', (e) => {
            this.settings.showTrajectory = e.target.checked;
            this.emit('settingsChanged', this.settings);
        });

        document.addEventListener('keydown', (e) => {
            this.handleKeyPress(e);
        });
    }

    handleKeyPress(event) {
        switch (event.code) {
            case 'Escape':
                if (this.currentPanel === 'game') {
                    this.emit('pause');
                } else if (this.currentPanel !== 'main') {
                    this.showMainMenu();
                }
                break;
            case 'F1':
                event.preventDefault();
                this.showHelp();
                break;
        }
    }

    initializeSettings() {
        if (this.elements.soundVolume) {
            this.elements.soundVolume.value = this.settings.soundVolume;
        }

        if (this.elements.graphicsQuality) {
            this.elements.graphicsQuality.value = this.settings.graphicsQuality;
        }

        if (this.elements.showTrajectory) {
            this.elements.showTrajectory.checked = this.settings.showTrajectory;
        }
    }

    showMainMenu() {
        this.hideAllPanels();
        this.elements.mainMenu?.classList.remove('hidden');
        this.currentPanel = 'main';
    }

    showGameHUD() {
        this.hideAllPanels();
        this.elements.gameHUD?.classList.remove('hidden');
        this.currentPanel = 'game';
    }

    showSettings() {
        this.hideAllPanels();
        this.elements.settingsPanel?.classList.remove('hidden');
        this.currentPanel = 'settings';
    }

    showHelp() {
        this.hideAllPanels();
        this.elements.helpPanel?.classList.remove('hidden');
        this.currentPanel = 'help';
    }

    showGameOver(winner) {
        this.hideAllPanels();
        this.elements.gameOverPanel?.classList.remove('hidden');

        if (this.elements.winnerText) {
            this.elements.winnerText.textContent = `玩家${winner} 获胜！`;
        }

        this.currentPanel = 'gameOver';
    }

    hideAllPanels() {
        const panels = [
            this.elements.mainMenu,
            this.elements.gameHUD,
            this.elements.settingsPanel,
            this.elements.helpPanel,
            this.elements.gameOverPanel
        ];

        panels.forEach(panel => {
            panel?.classList.add('hidden');
        });
    }

    updateScores(scores) {
        if (this.elements.player1Score) {
            this.elements.player1Score.textContent = scores.player1;
        }

        if (this.elements.player2Score) {
            this.elements.player2Score.textContent = scores.player2;
        }
    }

    updateCurrentPlayer(player) {
        if (this.elements.currentPlayerName) {
            this.elements.currentPlayerName.textContent = `玩家${player}`;
        }

        this.highlightCurrentPlayer(player);
    }

    highlightCurrentPlayer(player) {
        const scoreboard = document.getElementById('scoreboard');
        if (!scoreboard) return;

        const playerScores = scoreboard.querySelectorAll('.player-score');
        playerScores.forEach((scoreElement, index) => {
            if (index + 1 === player) {
                scoreElement.style.borderColor = '#4CAF50';
                scoreElement.style.backgroundColor = 'rgba(76, 175, 80, 0.1)';
            } else {
                scoreElement.style.borderColor = '#ffd700';
                scoreElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            }
        });
    }

    updatePower(power) {
        if (this.elements.powerFill) {
            this.elements.powerFill.style.width = `${power}%`;
        }

        if (this.elements.powerValue) {
            this.elements.powerValue.textContent = `${Math.round(power)}%`;
        }
    }

    showMessage(message, type = 'info', duration = 3000) {
        const messageElement = document.createElement('div');
        messageElement.className = `game-message ${type}`;
        messageElement.textContent = message;

        Object.assign(messageElement.style, {
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            background: type === 'error' ? 'rgba(244, 67, 54, 0.9)' : 'rgba(76, 175, 80, 0.9)',
            color: 'white',
            padding: '20px 30px',
            borderRadius: '10px',
            fontSize: '1.2em',
            fontWeight: 'bold',
            zIndex: '1000',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
            animation: 'fadeInOut 0.3s ease-in-out'
        });

        document.body.appendChild(messageElement);

        setTimeout(() => {
            if (messageElement.parentNode) {
                messageElement.parentNode.removeChild(messageElement);
            }
        }, duration);
    }

    destroy() {
        document.removeEventListener('keydown', this.handleKeyPress);
        this.elements = {};
        this.isInitialized = false;
    }
}

// 简化的Game类
class Game extends EventEmitter {
    constructor(mode = 'single') {
        super();

        this.mode = mode;
        this.isInitialized = false;
        this.isPaused = false;
        this.isGameOver = false;

        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.canvas = null;

        this.table = null;
        this.balls = [];
        this.physics = null;

        this.currentPlayer = 1;
        this.scores = { player1: 0, player2: 0 };
        this.power = 0;
        this.isAiming = false;
        this.isShooting = false;

        this.mouse = new THREE.Vector2();
        this.raycaster = new THREE.Raycaster();

        this.settings = {
            soundVolume: 50,
            graphicsQuality: 'medium',
            showTrajectory: true
        };

        this.animationId = null;
        this.clock = new THREE.Clock();
    }

    async init() {
        try {
            this.canvas = document.getElementById('game-canvas');
            if (!this.canvas) {
                throw new Error('找不到游戏canvas元素');
            }

            this.initThreeJS();

            this.physics = new Physics();
            await this.physics.init();

            this.table = new Table();
            await this.table.init(this.scene, this.physics);

            await this.createBalls();

            this.setupEventListeners();
            this.startGameLoop();

            this.isInitialized = true;
            console.log('游戏初始化完成');

        } catch (error) {
            console.error('游戏初始化失败:', error);
            throw error;
        }
    }

    initThreeJS() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x1a1a1a);

        const aspect = window.innerWidth / window.innerHeight;
        this.camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);
        this.camera.position.set(0, 8, 12);
        this.camera.lookAt(0, 0, 0);

        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            antialias: this.settings.graphicsQuality !== 'low'
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.physicallyCorrectLights = true;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1;

        this.setupLighting();
    }

    setupLighting() {
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(ambientLight);

        const mainLight = new THREE.DirectionalLight(0xffffff, 1);
        mainLight.position.set(10, 20, 10);
        mainLight.castShadow = true;
        mainLight.shadow.mapSize.width = 2048;
        mainLight.shadow.mapSize.height = 2048;
        mainLight.shadow.camera.near = 0.5;
        mainLight.shadow.camera.far = 50;
        mainLight.shadow.camera.left = -20;
        mainLight.shadow.camera.right = 20;
        mainLight.shadow.camera.top = 20;
        mainLight.shadow.camera.bottom = -20;
        this.scene.add(mainLight);

        const fillLight = new THREE.DirectionalLight(0x4080ff, 0.3);
        fillLight.position.set(-10, 10, -10);
        this.scene.add(fillLight);

        const spotLight = new THREE.SpotLight(0xffffff, 0.8);
        spotLight.position.set(0, 15, 0);
        spotLight.target.position.set(0, 0, 0);
        spotLight.angle = Math.PI / 3;
        spotLight.penumbra = 0.1;
        spotLight.decay = 2;
        spotLight.distance = 30;
        spotLight.castShadow = true;
        this.scene.add(spotLight);
        this.scene.add(spotLight.target);
    }

    async createBalls() {
        const cueBall = new Ball('cue', 0);
        await cueBall.init(this.scene, this.physics);
        cueBall.setPosition(-6, 0, 0);
        this.balls.push(cueBall);

        const ballPositions = this.getBallRackPositions();

        for (let i = 1; i <= 15; i++) {
            const ballType = i <= 7 ? 'solid' : (i === 8 ? 'eight' : 'stripe');
            const ball = new Ball(ballType, i);
            await ball.init(this.scene, this.physics);
            ball.setPosition(ballPositions[i - 1].x, ballPositions[i - 1].y, ballPositions[i - 1].z);
            this.balls.push(ball);
        }
    }

    getBallRackPositions() {
        const positions = [];
        const startX = 6;
        const startZ = 0;
        const ballRadius = 0.057;
        const spacing = ballRadius * 2.1;

        positions.push({ x: startX, y: ballRadius, z: startZ });

        positions.push({ x: startX + spacing, y: ballRadius, z: startZ - spacing / 2 });
        positions.push({ x: startX + spacing, y: ballRadius, z: startZ + spacing / 2 });

        positions.push({ x: startX + spacing * 2, y: ballRadius, z: startZ - spacing });
        positions.push({ x: startX + spacing * 2, y: ballRadius, z: startZ });
        positions.push({ x: startX + spacing * 2, y: ballRadius, z: startZ + spacing });

        positions.push({ x: startX + spacing * 3, y: ballRadius, z: startZ - spacing * 1.5 });
        positions.push({ x: startX + spacing * 3, y: ballRadius, z: startZ - spacing / 2 });
        positions.push({ x: startX + spacing * 3, y: ballRadius, z: startZ + spacing / 2 });
        positions.push({ x: startX + spacing * 3, y: ballRadius, z: startZ + spacing * 1.5 });

        positions.push({ x: startX + spacing * 4, y: ballRadius, z: startZ - spacing * 2 });
        positions.push({ x: startX + spacing * 4, y: ballRadius, z: startZ - spacing });
        positions.push({ x: startX + spacing * 4, y: ballRadius, z: startZ });
        positions.push({ x: startX + spacing * 4, y: ballRadius, z: startZ + spacing });
        positions.push({ x: startX + spacing * 4, y: ballRadius, z: startZ + spacing * 2 });

        return positions;
    }

    setupEventListeners() {
        this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
        this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
        this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
        this.canvas.addEventListener('wheel', this.onMouseWheel.bind(this));

        window.addEventListener('keydown', this.onKeyDown.bind(this));
        window.addEventListener('resize', this.handleResize.bind(this));
    }

    onMouseMove(event) {
        if (!this.isInitialized || this.isPaused || this.isGameOver) return;

        const rect = this.canvas.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    }

    onMouseDown(event) {
        if (!this.isInitialized || this.isPaused || this.isGameOver) return;

        if (event.button === 0) {
            this.startAiming();
        }
    }

    onMouseUp(event) {
        if (!this.isInitialized || this.isPaused || this.isGameOver) return;

        if (event.button === 0 && this.isAiming) {
            this.shoot();
        }
    }

    onMouseWheel(event) {
        if (!this.isInitialized) return;

        const delta = event.deltaY * 0.01;
        const direction = new THREE.Vector3();
        this.camera.getWorldDirection(direction);
        this.camera.position.addScaledVector(direction, delta);
    }

    onKeyDown(event) {
        if (!this.isInitialized) return;

        switch (event.code) {
            case 'Space':
                event.preventDefault();
                if (this.isPaused) {
                    this.resume();
                } else {
                    this.pause();
                }
                break;
            case 'KeyR':
                this.reset();
                break;
        }
    }

    startAiming() {
        if (this.isShooting || !this.canShoot()) return;

        this.isAiming = true;
        this.power = 0;
        this.startPowerAccumulation();
    }

    startPowerAccumulation() {
        const startTime = Date.now();
        const maxPower = 100;
        const accumTime = 2000;

        const updatePower = () => {
            if (!this.isAiming) return;

            const elapsed = Date.now() - startTime;
            this.power = Math.min((elapsed / accumTime) * maxPower, maxPower);

            this.emit('powerChange', this.power);

            if (this.isAiming) {
                requestAnimationFrame(updatePower);
            }
        };

        updatePower();
    }

    shoot() {
        if (!this.isAiming || this.power === 0) return;

        this.isAiming = false;
        this.isShooting = true;

        this.raycaster.setFromCamera(this.mouse, this.camera);
        const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0);
        const intersectPoint = new THREE.Vector3();
        this.raycaster.ray.intersectPlane(plane, intersectPoint);

        const cueBall = this.balls[0];
        const cueBallPos = cueBall.getPosition();

        const direction = new THREE.Vector3()
            .subVectors(intersectPoint, cueBallPos)
            .normalize();

        const force = (this.power / 100) * 15;

        cueBall.applyForce(direction, force);

        this.power = 0;
        this.emit('powerChange', this.power);

        this.waitForBallsToStop();
    }

    canShoot() {
        return this.balls.every(ball => ball.isStopped());
    }

    waitForBallsToStop() {
        const checkInterval = setInterval(() => {
            if (this.canShoot()) {
                clearInterval(checkInterval);
                this.isShooting = false;
                this.onTurnEnd();
            }
        }, 100);
    }

    onTurnEnd() {
        this.switchPlayer();
        this.updateScores();
    }

    switchPlayer() {
        this.currentPlayer = this.currentPlayer === 1 ? 2 : 1;
        this.emit('playerChange', this.currentPlayer);
    }

    updateScores() {
        this.emit('scoreUpdate', this.scores);
    }

    startGameLoop() {
        const animate = () => {
            if (!this.isInitialized) return;

            this.animationId = requestAnimationFrame(animate);

            if (!this.isPaused) {
                const deltaTime = this.clock.getDelta();

                if (this.physics) {
                    this.physics.update(deltaTime);
                }

                this.balls.forEach(ball => {
                    ball.update();
                });
            }

            this.renderer.render(this.scene, this.camera);
        };

        animate();
    }

    pause() {
        this.isPaused = true;
        this.emit('pause');
    }

    resume() {
        this.isPaused = false;
        this.emit('resume');
    }

    reset() {
        this.currentPlayer = 1;
        this.scores = { player1: 0, player2: 0 };
        this.power = 0;
        this.isAiming = false;
        this.isShooting = false;
        this.isGameOver = false;

        this.resetBallPositions();

        this.emit('reset');
        this.emit('playerChange', this.currentPlayer);
        this.emit('scoreUpdate', this.scores);
        this.emit('powerChange', this.power);
    }

    resetBallPositions() {
        this.balls[0].setPosition(-6, 0, 0);
        this.balls[0].stop();

        const ballPositions = this.getBallRackPositions();
        for (let i = 1; i < this.balls.length; i++) {
            const pos = ballPositions[i - 1];
            this.balls[i].setPosition(pos.x, pos.y, pos.z);
            this.balls[i].stop();
        }
    }

    updateSettings(settings) {
        this.settings = { ...this.settings, ...settings };

        if (settings.graphicsQuality) {
            this.updateGraphicsQuality(settings.graphicsQuality);
        }
    }

    updateGraphicsQuality(quality) {
        switch (quality) {
            case 'low':
                this.renderer.shadowMap.enabled = false;
                this.renderer.antialias = false;
                break;
            case 'medium':
                this.renderer.shadowMap.enabled = true;
                this.renderer.antialias = false;
                break;
            case 'high':
                this.renderer.shadowMap.enabled = true;
                this.renderer.antialias = true;
                break;
        }
    }

    handleResize() {
        if (!this.camera || !this.renderer) return;

        const width = window.innerWidth;
        const height = window.innerHeight;

        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();

        this.renderer.setSize(width, height);
    }

    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }

        if (this.canvas) {
            this.canvas.removeEventListener('mousemove', this.onMouseMove);
            this.canvas.removeEventListener('mousedown', this.onMouseDown);
            this.canvas.removeEventListener('mouseup', this.onMouseUp);
            this.canvas.removeEventListener('wheel', this.onMouseWheel);
        }

        window.removeEventListener('keydown', this.onKeyDown);
        window.removeEventListener('resize', this.handleResize);

        if (this.scene) {
            this.scene.clear();
        }

        if (this.renderer) {
            this.renderer.dispose();
        }

        this.balls.forEach(ball => ball.destroy());
        this.balls = [];

        if (this.table) {
            this.table.destroy();
        }

        if (this.physics) {
            this.physics.destroy();
        }

        this.isInitialized = false;
    }
}

// App类
class App {
    constructor() {
        this.game = null;
        this.ui = null;
        this.isInitialized = false;

        this.init();
    }

    async init() {
        try {
            this.showLoading();

            this.ui = new UI();
            await this.ui.init();

            this.setupUIEvents();

            this.hideLoading();
            this.ui.showMainMenu();

            this.isInitialized = true;
            console.log('台球游戏初始化完成');

        } catch (error) {
            console.error('游戏初始化失败:', error);
            this.showError('游戏初始化失败，请刷新页面重试: ' + error.message);
        }
    }

    setupUIEvents() {
        this.ui.on('singlePlayer', () => {
            this.startGame('single');
        });

        this.ui.on('twoPlayer', () => {
            this.startGame('two');
        });

        this.ui.on('settings', () => {
            this.ui.showSettings();
        });

        this.ui.on('help', () => {
            this.ui.showHelp();
        });

        this.ui.on('pause', () => {
            if (this.game) {
                this.game.pause();
            }
        });

        this.ui.on('reset', () => {
            if (this.game) {
                this.game.reset();
            }
        });

        this.ui.on('backToMenu', () => {
            if (this.game) {
                this.game.destroy();
                this.game = null;
            }
            this.ui.showMainMenu();
        });

        this.ui.on('playAgain', () => {
            if (this.game) {
                this.game.reset();
                this.ui.showGameHUD();
            }
        });

        this.ui.on('settingsChanged', (settings) => {
            if (this.game) {
                this.game.updateSettings(settings);
            }
        });
    }

    async startGame(mode) {
        try {
            this.showLoading();

            if (this.game) {
                this.game.destroy();
            }

            this.game = new Game(mode);
            await this.game.init();

            this.setupGameEvents();

            this.ui.showGameHUD();
            this.hideLoading();

            console.log(`开始${mode === 'single' ? '单人' : '双人'}游戏`);

        } catch (error) {
            console.error('游戏启动失败:', error);
            this.showError('游戏启动失败，请重试: ' + error.message);
            this.hideLoading();
        }
    }

    setupGameEvents() {
        if (!this.game) return;

        this.game.on('scoreUpdate', (scores) => {
            this.ui.updateScores(scores);
        });

        this.game.on('playerChange', (player) => {
            this.ui.updateCurrentPlayer(player);
        });

        this.game.on('powerChange', (power) => {
            this.ui.updatePower(power);
        });

        this.game.on('gameOver', (winner) => {
            this.ui.showGameOver(winner);
        });

        this.game.on('foul', (message) => {
            this.ui.showMessage(message);
        });

        this.game.on('error', (error) => {
            console.error('游戏错误:', error);
            this.showError('游戏出现错误: ' + error.message);
        });
    }

    showLoading() {
        const loading = document.getElementById('loading');
        if (loading) {
            loading.classList.remove('hidden');
        }
    }

    hideLoading() {
        const loading = document.getElementById('loading');
        if (loading) {
            loading.classList.add('hidden');
        }
    }

    showError(message) {
        alert(message);
    }

    handleResize() {
        if (this.game) {
            this.game.handleResize();
        }
    }

    destroy() {
        if (this.game) {
            this.game.destroy();
        }
        if (this.ui) {
            this.ui.destroy();
        }
    }
}

// 全局事件监听
window.addEventListener('resize', () => {
    if (window.app) {
        window.app.handleResize();
    }
});

window.addEventListener('beforeunload', () => {
    if (window.app) {
        window.app.destroy();
    }
});

// 启动应用
window.addEventListener('load', () => {
    window.app = new App();
});
