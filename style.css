* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    overflow: hidden;
    color: white;
}

#game-container {
    position: relative;
    width: 100vw;
    height: 100vh;
}

#game-canvas {
    display: block;
    width: 100%;
    height: 100%;
}

#game-ui {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.menu-panel {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    pointer-events: all;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.menu-panel h1 {
    font-size: 3em;
    margin-bottom: 30px;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.menu-panel h2 {
    font-size: 2em;
    margin-bottom: 20px;
    color: #ffd700;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.menu-btn {
    padding: 15px 30px;
    font-size: 1.2em;
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-weight: bold;
}

.menu-btn:hover {
    background: linear-gradient(45deg, #45a049, #4CAF50);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

#game-hud {
    pointer-events: all;
}

#scoreboard {
    position: absolute;
    top: 20px;
    left: 20px;
    display: flex;
    gap: 30px;
}

.player-score {
    background: rgba(0, 0, 0, 0.7);
    padding: 15px 20px;
    border-radius: 10px;
    text-align: center;
    border: 2px solid #ffd700;
}

.player-name {
    display: block;
    font-size: 1.1em;
    margin-bottom: 5px;
}

.score {
    display: block;
    font-size: 2em;
    font-weight: bold;
    color: #ffd700;
}

#current-player {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    padding: 15px 20px;
    border-radius: 10px;
    font-size: 1.2em;
    border: 2px solid #4CAF50;
}

#power-control {
    position: absolute;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    min-width: 300px;
}

#power-bar {
    width: 200px;
    height: 20px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    margin: 10px auto;
    position: relative;
    overflow: hidden;
}

#power-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #FFC107, #F44336);
    border-radius: 10px;
    width: 0%;
    transition: width 0.1s ease;
}

#game-controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
}

.control-btn {
    padding: 10px 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: 2px solid #ffd700;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(255, 215, 0, 0.2);
}

.setting-item {
    margin: 20px 0;
    text-align: left;
}

.setting-item label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.setting-item input,
.setting-item select {
    width: 100%;
    padding: 8px;
    border-radius: 5px;
    border: 1px solid #ccc;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
}

.help-content {
    text-align: left;
    max-width: 500px;
}

.help-content h3 {
    color: #ffd700;
    margin: 20px 0 10px 0;
}

.help-content ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.help-content li {
    margin-bottom: 5px;
    line-height: 1.4;
}

.hidden {
    display: none !important;
}

#loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-top: 5px solid #ffd700;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin-top: 20px;
    font-size: 1.2em;
    color: #ffd700;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .menu-panel {
        padding: 20px;
        width: 90%;
    }
    
    .menu-panel h1 {
        font-size: 2em;
    }
    
    #scoreboard {
        flex-direction: column;
        gap: 10px;
    }
    
    #power-control {
        bottom: 80px;
        min-width: 250px;
    }
    
    #game-controls {
        bottom: 10px;
        right: 10px;
        flex-direction: column;
    }
}
